#include "senderMMSProcessDB.h"
#include <sys/time.h>
//#include <time.h>
static CMMSPacketSend mmsPacketSend;
static int S_PROCESS_NO;

int main(int argc,char* argv[])
{
	
	/*
	 * 1 : sockfd
	 * 2 : pipe
	 * 3 : version
	 * 4 : conf file
	 */
	int sockfd;
	int fd;
	int ret;
	char buff[SOCKET_BUFF];
	CLogonDbInfo logonDbInfo;
	
	sockfd = atoi(argv[1]);
	fd = atoi(argv[2]);
	memset(&logonDbInfo,0x00,sizeof(logonDbInfo));
	read(fd,(char*)&logonDbInfo,sizeof(logonDbInfo));
	close(fd);
	
	
	memset(_DATALOG,0x00,sizeof(_DATALOG));//CCL(_DATALOG);
	memset(_MONILOG,0x00,sizeof(_MONILOG));//CCL(_MONILOG);
	char* p;
	
	sprintf(_DATALOG,"%s/",logonDbInfo.szLogPath);
	sprintf(_MONILOG,"%s/",logonDbInfo.szLogPath);
	
	memset(szSenderID,0x00,sizeof(szSenderID));//CCL(szSenderID);
	strcpy(szSenderID,logonDbInfo.szCID);
	S_PROCESS_NO = getpid();
	
	p = strtok(logonDbInfo.szLogFilePath,"|");
	if( p )
	{
		strcat(_MONILOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get monitor [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	p = strtok(NULL,"|");
	if( p )
	{
		strcat(_DATALOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get data [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	logPrintS(0,"[INF] filepath - logfile[%s] monitorfile[%s] PID[%d]",_DATALOG,_MONILOG, S_PROCESS_NO);
		
	ret = configParse(argv[4]);
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] configParse Failed");
	    exit(1);
	}
	
	logPrintS(0,"[INF] config file - logonDBName [%s]",gConf.logonDBName);
	
	if (g_oracle.connectToOracle(gConf.dbuid, gConf.dbdsn)<0)
	{
		logPrintS(0,"[ERR] connectToOracle Failed");
	    return -1;
	}
	
	logPrintS(1,"[INF] ORACLE CONNECT");
	
	SenderProcess *mSenderProcess = new SenderProcess();

	// sendre Main Process Start 
	mSenderProcess->SenderMain(sockfd,logonDbInfo);
	
	if (g_oracle.closeFromOracle()<0)
	{
		logPrintS(0,"[ERR] closeFromOracle Failed");
	    return -1;
	}
	logPrintS(1,"[INF] ORACLE DISCONNECT");
	
	return 0;
}


int sendAck(CKSSocket& hRemoteSock,CMMSPacketSend& mmsPacketSend, int nCode,int ctnid,string strDesc)
{
	int ret=0;
	string strPacket;
	strPacket = "";
	strPacket.reserve(0);
	string key;
	char szCode[8];

	memset(szCode		,0x00	,sizeof(szCode));

	sprintf(szCode,"%d",nCode);

	key = mmsPacketSend.getKeyValue();

	strPacket = "BEGIN ACK\r\nKEY:" + key + "\r\nCODE:" ;
	strPacket += szCode;
	strPacket += "\r\nDESC:" + strDesc + "\r\nEND\r\n";
		
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	logPrintS(1,"[INF] socket ack ctnid[%d]key[%s]code[%s]strDesc[%s]", ctnid, key.c_str(), szCode, strDesc.c_str() );

	return ret;
}

int sendPong(CKSSocket& hRemoteSock)
{
	string strPacket;
	string strKey;
	CMMSPacketBase packetBase;
	int ret;
	
	packetBase.findValue((char*)hRemoteSock.getMsg(),"KEY",strKey);

	strPacket = "BEGIN PONG\r\nKEY:" + strKey + "\r\nEND\r\n";
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	
	if( ret != strPacket.length() )
	{
		logPrintS(0,"[ERR] socket ack send failed sendSize/packetSize[%d/%d]",ret,strPacket.length());
		return ret;
	}
	//logPrintS(0,"[INF] socket link PONG send");
  
	fflush(stdout);
	return 0;
}

/** @return 음수 일시 프로세스 종료 */
int recvLink(CKSSocket& hRemoteSock,char* buff)
{
	int ret;

	TypeMsgDataAck* pLinkAck = (TypeMsgDataAck*)buff;
	memset(pLinkAck->header.msgType, 0x00, sizeof(pLinkAck->header.msgType));
	strcpy(pLinkAck->header.msgType,"8");

	ret = hRemoteSock.send(buff,sizeof(TypeMsgDataAck));
	if( ret != sizeof(TypeMsgDataAck))
	{
		logPrintS(0,"[ERR] socket link ack send failed - errno[%s] sendSize/packetSize[%d/%d]", strerror(errno), ret, sizeof(TypeMsgDataAck));
		return -1;
	}
	time(&SLastTLink);

	return 0;
}

void logPrintS(int type, const char *format, ...)
{
	va_list args;
	char logMsg[SOCKET_BUFF];
	char tmpMsg[SOCKET_BUFF];

	va_start(args, format);
	vsprintf(tmpMsg, format, args);
	va_end(args);

	sprintf(logMsg,"[S][%s] %s",szSenderID,tmpMsg);
	if (type==1)
	{
		_logPrint(_DATALOG,logMsg);
	}
	else
	{
		_monPrint(_MONILOG,logMsg);
	}
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;

	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
	if( gConf.logonDBName == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	
	conf.strncpy2(gConf.monitorName , conf.FetchEntry("domain.monitor"),64);
	
	if( gConf.monitorName == NULL )
	{
		strcpy(gConf.monitorName,"");
	}

	conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.self"),64);
	if( gConf.domainPath == NULL )
	{
		strcpy(gConf.domainPath,"");
	}

	conf.strncpy2(gConf.ContentPath , conf.FetchEntry("path.mmscontent"),64);
	
	if( gConf.ContentPath == NULL )
	{
		strcpy(gConf.ContentPath,"");
	}

	gConf.socketLinkTimeOut = conf.FetchEntryInt("socket.linktimeout");
	
	if( gConf.socketLinkTimeOut <= 1 )
	{
		gConf.socketLinkTimeOut = 2;
	}

	gConf.dbRequestTimeOut = conf.FetchEntryInt("db.requesttimeout");

	if( gConf.dbRequestTimeOut <= 0 )
	{
		gConf.dbRequestTimeOut = 1;
	}
	
	conf.strncpy2(gConf.dbuid , conf.FetchEntry("db.uid"),64);
	
	if( gConf.dbuid == NULL )
	{
		strcpy(gConf.dbuid,"");
	}

	conf.strncpy2(gConf.dbdsn , conf.FetchEntry("db.dsn"),64);
	
	if( gConf.dbdsn == NULL )
	{
		strcpy(gConf.dbuid,"");
	}
	
	return 0;
}


void viewPackSender(char *a,int n)
{
	int i;
	char logMsg[VIEWPACK_MAX_SIZE];
	char strtmp[VIEWPACK_MAX_SIZE];
	
	memset(logMsg,0x00, sizeof logMsg);
	memset(strtmp,0x00, sizeof strtmp);
	
	for(i=0;i<n;i++)
	{
		if( a[i] == 0x00 )
		{
			strtmp[i] = '.';
	    }
	    else
		{
			memcpy(strtmp+i,a+i,1);
		}
	}
	
	sprintf(logMsg,"info:[%s]",strtmp);
	_monPrint(_MONILOG,logMsg);
	
	return ;
}


int setMMSCTNTBL2DB(CMMSFileProcess& mmsFileProcess)
{
	int ret;
	CSenderDbMMSCTNTBL senderDbMMSCTNTBL;
	
	CMMSCtnTbl mmsCtnTbl;
	
	ret = mmsFileProcess.getMMSCtnTblFirst(mmsCtnTbl);
	if( ret != 0 ) return 1; 
	
	while(ret == 0 )
	{
		memset(&senderDbMMSCTNTBL,0x00,sizeof(CSenderDbMMSCTNTBL));	
		senderDbMMSCTNTBL.nCtnId = mmsCtnTbl.nCtnId;
		memcpy(senderDbMMSCTNTBL.szCtnName, mmsCtnTbl.szCtnName, sizeof(mmsCtnTbl.szCtnName));
		memcpy(senderDbMMSCTNTBL.szCtnMime, mmsCtnTbl.szCtnMime, sizeof(mmsCtnTbl.szCtnMime));
		senderDbMMSCTNTBL.nCtnSeq = mmsCtnTbl.nCtnSeq;
		memcpy(senderDbMMSCTNTBL.szCtnSvc , mmsCtnTbl.szCtnSvc , sizeof(mmsCtnTbl.szCtnSvc ));
	
		ret = g_oracle.setMMSCTNTBL(senderDbMMSCTNTBL);
		if (ret <= 0)
		{
			logPrintS(1,"[ERR] setMMSCTNTBL [%d]", ret);
		}
		ret = mmsFileProcess.getMMSCtnTblNext(mmsCtnTbl);
	}

	return 0; // OK

}

int setMMSMSG2DB(char* szCid, long long nMMSId,int ctnid, CMMSFileProcess& mmsFileProcess,int priority,CBcastData * pBcastData)
{
	int ret;
	CSenderDbMMSMSG senderDbMMSMSG;
	
	memset(&senderDbMMSMSG,0x00,sizeof(CSenderDbMMSMSG));
	sprintf(senderDbMMSMSG.szQName,"%d", getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, mmsFileProcess.getTxtColorYN()));
	
	senderDbMMSMSG.nPriority = priority;
	senderDbMMSMSG.nCtnId = ctnid;

	if( pBcastData == NULL)
	{
		strcpy(senderDbMMSMSG.szDstAddr,mmsPacketSend.getReceiverValue());
	}
	else
	{
		strcpy(senderDbMMSMSG.szDstAddr,pBcastData->strReceiver.c_str());
	}

	strcpy(senderDbMMSMSG.szCallBack,mmsPacketSend.getSenderValue());
	strcpy(senderDbMMSMSG.szMsgTitle,mmsPacketSend.getSubjectValue());
	senderDbMMSMSG.nCntType = mmsPacketSend.getCtnType();

	if( strcmp(senderDbMMSMSG.szQName,"1") == 0 ) 
	{
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_SKT/%s",mmsFileProcess.getTxtPath());
	} 
	else if ( strcmp(senderDbMMSMSG.szQName,"2") == 0 ) 
	{
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_KTF/%s",mmsFileProcess.getTxtPath());
	} 
	else if  ( strcmp(senderDbMMSMSG.szQName,"3") == 0 ) 
	{
		//sprintf(senderDbMMSMSG.szTxtPath,"TXT_LGT/%s",mmsFileProcess.getTxtPath());
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
	} 
	else if  ( strcmp(senderDbMMSMSG.szQName,"4") == 0 ) 
	{
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
	} 
	else if  ( strcmp(senderDbMMSMSG.szQName,"21") == 0 ) 
	{
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
	} 
	else if  ( strcmp(senderDbMMSMSG.szQName,"25") == 0 ) 
	{
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
	} 
	else 
	{
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
	}
		

	// 이미지만 전송할 경우 201309
	if(mmsPacketSend.getTextCnt() == 0 && mmsPacketSend.getImgCnt() >= 1)
	{
		memset(senderDbMMSMSG.szTxtPath, 0x00, sizeof(senderDbMMSMSG.szTxtPath));
	}
		
	senderDbMMSMSG.nRgnRate = 65;
	senderDbMMSMSG.nInterval = 10;
	senderDbMMSMSG.nMMSId = nMMSId;

	// 재판매사 - 전송받은 식별코드
	if (gSenderInfo.szSellerFlag[0] == 'Y') {
		strcpy(senderDbMMSMSG.szIdCode,	mmsPacketSend.getIdCode());
	}
	else {
		// 일반업체 - 자사 식별코드
		memcpy(senderDbMMSMSG.szIdCode,	gSenderInfo.szIdCode, 12);
	}
#if (DEBUG >= 3)
	logPrintS(1,"[DEB] gSenderInfo.szSellerFlag [%c]", gSenderInfo.szSellerFlag[0]);
	logPrintS(1,"[DEB] gSenderInfo.szIdCode     [%s]", gSenderInfo.szIdCode);
	logPrintS(1,"[DEB] mmsPacketSend.getIdCode()[%s]", mmsPacketSend.getIdCode());
	logPrintS(1,"[DEB] senderDbMMSMSG.szIdCode  [%s]", senderDbMMSMSG.szIdCode);
#endif

	ret = g_oracle.setMMSMSG(senderDbMMSMSG);
	if (ret <= 0)
	{
		logPrintS(1,"[ERR] setMMSMSG [%d]", ret);
		return ret;
	}
	else
	{
		ret = 100;	
		return ret;
	}		
	
}

int setMMSTBL2DB(long long nMMSId, int ctnid,int priority,CBcastData * pBcastData)
{
	int ret;
	CSenderDbMMSTBL senderDbMMSTBL;

	memset(&senderDbMMSTBL,0x00,sizeof(CSenderDbMMSTBL));

	strcpy(senderDbMMSTBL.szCallBack,mmsPacketSend.getSenderValue());
	if( pBcastData == NULL )
	{
		strcpy(senderDbMMSTBL.szDstAddr,mmsPacketSend.getReceiverValue());
	}
	else
	{
		strcpy(senderDbMMSTBL.szDstAddr,pBcastData->strReceiver.c_str());
	}
	strcpy(senderDbMMSTBL.szMsgTitle,mmsPacketSend.getSubjectValue());

	if( pBcastData == NULL )
	{
		strcpy(senderDbMMSTBL.szPtnSn,mmsPacketSend.getKeyValue());
	}
	else
	{
		strcpy(senderDbMMSTBL.szPtnSn,pBcastData->strKey.c_str());
	}

	if( pBcastData == NULL )
	{
		strcpy(senderDbMMSTBL.szResvData,mmsPacketSend.getExtendValue());
	}
	else
	{
		strcpy(senderDbMMSTBL.szResvData,pBcastData->strExtend.c_str());
	}

	strcpy(senderDbMMSTBL.szCid,szSenderID);
	senderDbMMSTBL.nMsgType 	= 0;
	senderDbMMSTBL.nPriority 	= priority;
	senderDbMMSTBL.nCtnId 		= ctnid;
	senderDbMMSTBL.nCtnType 	= mmsPacketSend.getCtnType();
	senderDbMMSTBL.nRgnRate 	= 65;
	senderDbMMSTBL.nInterval 	= 10;
	senderDbMMSTBL.nTextCnt 	= mmsPacketSend.getTextCnt();
	senderDbMMSTBL.nImgCnt 		= mmsPacketSend.getImgCnt();
	senderDbMMSTBL.nAugCnt 		= mmsPacketSend.getAugCnt();
	senderDbMMSTBL.nMpCnt 		= mmsPacketSend.getMpCnt();
	senderDbMMSTBL.nMMSId 		= nMMSId;

	// 재판매사 - 전송받은 식별코드
	if (gSenderInfo.szSellerFlag[0] == 'Y') {
		strcpy(senderDbMMSTBL.szIdCode,	mmsPacketSend.getIdCode());
	}
	else {
		// 일반업체 - 자사 식별코드
		memcpy(senderDbMMSTBL.szIdCode,	gSenderInfo.szIdCode, 12);
	}

	ret = g_oracle.setMMSTBL(senderDbMMSTBL);
	if (ret <= 0)
	{
		logPrintS(1,"[ERR] setMMSTBL [%d]", ret);
	}
	
	return ret;
}


/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getCTNID2DB(CSenderDbMMSID& senderDbMMSID)
{
	int ret = 0;
	if(mmsPacketSend.getImgCnt() == 0)
	{
		senderDbMMSID.ctnid = -1;
		return 0;
	}
		
	senderDbMMSID.ctnid = g_oracle.getCTNID();
	if(senderDbMMSID.ctnid <= 0 )
	{
		logPrintS(1,"[ERR] socket_domain get ctn id to db failed - ctnid[%d]", senderDbMMSID.ctnid);
		// 2013.12 LSY 보완 필요
		senderDbMMSID.ctnid = 999999999;
		
		return 0;
	}
	if(senderDbMMSID.ctnid <= 0)
	{
		return -1;
	}
	return 1;
}
/**====================================================================================**/
// 2014020 make mmsid
/**====================================================================================**/
long long mkMMSID(void)
{
//	char	*pch;
//	pch		=(char*)malloc(sizeof(char)*100);
	char	pch[30];
	char	pchlast[30];
	
	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;
	
	long long ulltm;
	int nRand;
	
//	time(&the_time);					//불필요한 초기 시간 제거용으로 사용
//	tm_ptr = localtime(&val.tv_sec);	//불필요한 초기 시간 제거용으로 사용	
//	gettimeofday(&val,NULL);			//불필요한 초기 시간 제거용으로 사용
//	
//	time(&the_time);
//	tm_ptr = localtime(&val.tv_sec);
//	gettimeofday(&val,NULL);

	memset(pch				,0x00		,sizeof(pch));	
	
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);
	sprintf(pch, "%2d%02d%02d%02d%06ld"
				,tp.tm_mday+10
				,tp.tm_hour
				,tp.tm_min
				,tp.tm_sec
				,tmv.tv_nsec
				);
		
//	sprintf(pch,"%2d%02d%02d%02d%06ld"
//				//,tm_ptr->tm_year+1900
//				//,tm_ptr->tm_mon+1
//				,tm_ptr->tm_mday+10
//				,tm_ptr->tm_hour
//				,tm_ptr->tm_min
//				,tm_ptr->tm_sec
//				,val.tv_usec
//				);
//	ulltm = atoll(pch);
	
	//srand(val.tv_usec);
	srand(tmv.tv_nsec+S_PROCESS_NO);
	nRand = (rand()%100);
	
	memset(pchlast		,0x00		,sizeof(pchlast));
	sprintf(pchlast,"%.14s%02d",pch,nRand);
	
	ulltm = atoll(pchlast);
//	free(pch);
	return ulltm;
	
}

/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getMMSID2DB(CSenderDbMMSID& senderDbMMSID, char* cid)
{
	int ret = 0;
	senderDbMMSID.mmsid 		= 0;
	memcpy(senderDbMMSID.szCid,cid,10);
	senderDbMMSID.mmsid = mkMMSID();

	/**====================================================================================**/
	// 20140212 : MOD if div
	/**====================================================================================**/
	//if(senderDbInfoAck.mmsid == 0 || senderDbInfoAck.mmsid < 0)
	if( senderDbMMSID.mmsid == 0 )		
	{
		logPrintS(1,"[ERR] socket_domain get mms id to db failed - mmsid[%lld]", senderDbMMSID.mmsid);
		// 2013.12 LSY 보완 필요
		senderDbMMSID.mmsid = 999999999;
		
		return 0;	// 20140212 : ADD return
	}

	if( senderDbMMSID.mmsid < 0 )
	{
		return -1;
	}

	return 1;
	/**====================================================================================**/
}


int setRPTTBL2DB(long long nMMSId, int ctnid, int priority, int _code, char *code_text)
{
	int ret;
	time_t ThisT,LastT;
	CSenderDbMMSRPTTBL senderDbRPTTBL;
	time(&LastT);

	memset(&senderDbRPTTBL, 0x00, sizeof(CSenderDbMMSRPTTBL));

	senderDbRPTTBL.header.type = SETRPTTBL;
	senderDbRPTTBL.header.leng = sizeof(CSenderDbMMSRPTTBL) - sizeof(Header);
	
	senderDbRPTTBL.nMMSId = nMMSId;
	sprintf(senderDbRPTTBL.szCallBack, mmsPacketSend.getSenderValue());
	sprintf(senderDbRPTTBL.szDstAddr,mmsPacketSend.getReceiverValue());

	senderDbRPTTBL.res_code = _code;
	sprintf(senderDbRPTTBL.res_text, code_text);

	ret = g_oracle.setRPTTBL(senderDbRPTTBL);

	return ret;
}

int getTelcoId(int imgCnt, char* szTelco, int nColorYN)
{
	char* p;
	int telcoArray[3];
	int i=0;
	
	memset(telcoArray,0x00,sizeof(telcoArray));

	p = strtok(szTelco,"|");
	
	if( p == NULL )
	{
		return 1;
	}
	telcoArray[i++] = atoi(p);

	while(p = strtok(NULL,"|") )
	{
		telcoArray[i++]= atoi(p);
		if( i >= 3 )
		{
			break;
		}
	}

	//szTelco 다시 복구.버그 수정.2012.12.10.
	sprintf(szTelco, "%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2]);

	//컬러 문자를 우선으로 통신사를 체크한다. (컬러 LMS/MMS)
	if (nColorYN == 1)
	{
		return telcoArray[2];			//컬러
	}
	else if (imgCnt == 0)				//일반 LMS
	{
		return telcoArray[0];			//LMS
	}
	else if (imgCnt > 0)				//일반 MMS
	{
		return telcoArray[1];			//MMS
	}

	return 1;
	
}


void writeLogMMSData(CMMSPacketSend& mmsPacketSend,long long mmsid, int ctnid)
{
    logPrintS(1,"[INF] send message mmsid[%lld]ctnid[%d]key[%s]extend[%s]subject[%s]dst[]call[%s]contentCnt[%s]psktyn[%s]pktfyn[%s]plgtyn[%s] txt[%s] img[%s] mp[%s] bcast[%s]",
            mmsid,
            ctnid,
            mmsPacketSend.getKeyValue(),
            mmsPacketSend.getExtendValue(),
            mmsPacketSend.getSubjectValue(),
         /*   mmsPacketSend.getReceiverValue(),*/
            mmsPacketSend.getSenderValue(),
            mmsPacketSend.getContentCntValue(),
            mmsPacketSend.getPsktynValue(),
            mmsPacketSend.getPktfynValue(),
            mmsPacketSend.getPlgtynValue(),
            mmsPacketSend.getTextCntValue(),
            mmsPacketSend.getImgCntValue(),
            //mmsPacketSend.getAudCntValue(),
            mmsPacketSend.getMpCntValue(),
            mmsPacketSend.getBcastCntValue()
            );
}


SenderProcess::SenderProcess()
{
	bDayWarnCheck=false;
	bMonWarnCheck=false;
}

void SenderProcess::SenderMain(int sockfd,CLogonDbInfo& logonDbInfo)
{
	int ret;
	CLogonUtil util;
	CAdminUtil admin;
	CKSSocket db;
	CProcessInfo processInfo;
	CMonitor monitor;

	memset(&processInfo,0x00,sizeof(processInfo));
	memset(&gSenderInfo,0x00,sizeof(gSenderInfo));
	  
	strcpy(processInfo.processName,logonDbInfo.szSenderName);
	get_timestring("%04d%02d%02d%2d%2d%2d",time(NULL),processInfo.startTime);
	
	sprintf(processInfo.szPid,"%d",getpid());
	strcpy(processInfo.logonDBName,gConf.logonDBName);
	
	util.findValueParse(logonDbInfo.szReserve, "mms_tel", gSenderInfo.szSmsTelcoInfo);
	util.findValueParse(logonDbInfo.szReserve, "mms_yn",  gSenderInfo.szSmsFlag);

	//변작금지 flag 추가
	util.findValueParse(logonDbInfo.szReserve, "chk_callback", gSenderInfo.szChkCallback);
	
	strcpy(gSenderInfo.szUrlTelcoInfo,"0");
	strcpy(gSenderInfo.szUrlFlag,"0");

	// 식별코드 추가
	util.findValueParse(logonDbInfo.szReserve,	"sell", gSenderInfo.szSellerFlag);
    util.findValueParse(logonDbInfo.szReserve,	"idcd", gSenderInfo.szIdCode);
	
	logPrintS(0,"[INF] send process sender main start sockfd[%d]CID[%s]processInfo.startTime[%s]pid[%s]logonDbInfo.Reserve[%s]senderInfo[%s]smsFlag[%s]UrlTelcoInfo[%s]UrlFlag[%s]ChkCallback[%s] szSellerFlag[%s] szIdCode[%s]"
            ,sockfd
            ,logonDbInfo.szCID
            ,processInfo.startTime
            ,processInfo.szPid
            ,logonDbInfo.szReserve
            ,gSenderInfo.szSmsTelcoInfo
            ,gSenderInfo.szSmsFlag
            ,gSenderInfo.szUrlTelcoInfo
            ,gSenderInfo.szUrlFlag
			,gSenderInfo.szChkCallback
			,gSenderInfo.szSellerFlag
			,gSenderInfo.szIdCode
			);
	
	util.displayLogonDbInfo(logonDbInfo,_MONILOG);

	//20150610 변작금지-콜백리스트 가져오기
	if(strncmp(gSenderInfo.szChkCallback, "1", 1) == 0)
	{
		logPrintS(1, "ChkCallback[%d]:회신번호 검사", gSenderInfo.szChkCallback);
		ret = g_oracle.selectTblCallback(checkCallback.set_callback_list, logonDbInfo.nmPID);
		if(ret < 0)
		{
			logPrintS(1, "ret[%d]g_oracle.selectTblCallback() error", ret);
			return;
		}
	}
	else if(strncmp(gSenderInfo.szChkCallback, "2", 1) == 0)
	{
		logPrintS(1, "ChkCallback[%d]:번호체계 검사", gSenderInfo.szChkCallback);
		ret = loadDialCodeAll();

		if(ret < 0)
		{
			logPrintS(1, "ret[%d]load dial code error", ret);
			return;
		}
	}
	
	CKSSocket hRemoteSock;

	int recvLen;
   
	hRemoteSock.attach(sockfd);
   
	ret = admin.createDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] socket_domain create failed - CID[%s]classify[%c]domain_path[%s]", logonDbInfo.szCID, logonDbInfo.classify, gConf.domainPath);
        goto SenderEND;
	}
	
	monitor.Init("logon7", "sender", processInfo.processName, logonDbInfo.szCID, logonDbInfo.nmPID, logonDbInfo.szIP);
	time(&SLastTLink);
	
	nCurAccCnt = 0;

	memset(szLimitTime, 0x00, sizeof(szLimitTime));
	memset(szLimitCurTime, 0x00, sizeof(szLimitCurTime));
    
	while(bSActive)
	{
		//logPrintS(1,"[STP] -0- []");
				
		// 시간측정로그 : 메시지 수신전 시간
		/*				
	 	struct tm *d;
		struct timeval val;
		gettimeofday(&val, NULL);
		d=localtime(&val.tv_sec);		
		logPrintS(1,"0[Msg Recv Before]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
		*/					

		// 10초 대기 Admin 패킷 체크 문제 발생 3일때는 1000usec 대기 20240925
		//ret = hRemoteSock.recvAllMsg(10);
		ret = hRemoteSock.recvAllMsg(3);
		//ret = util.recvPacket(hRemoteSock,buff,0,10000);
		if( ret < 0)
		{
			logPrintS(1,"[ERR] socket read msg failed - [%s][%d]",hRemoteSock.getErrMsg(), errno);
			logPrintS(0,"[ERR] socket read msg failed - [%s][%d]",hRemoteSock.getErrMsg(), errno);
			goto SenderEND;
		}
		      
		if( ret == 0 )
		{
			//wait_a_moment(logonDbInfo.nmCNT);
			ret = admin.checkPacket(processInfo,logonDbInfo,sum);// check admin packet
			if( ret < 0 )
			{
				logPrintS(0,"[ERR] socket_domain packet check failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
				goto SenderEND;
			}
			//logPrintS(1,"[STP] -1- []");
	
			switch(ret) 
			{
				case 3: // end
					bSActive = false;
					continue;

				case 5: // info
					memset(gSenderInfo.szSmsTelcoInfo, 0x00, sizeof(gSenderInfo.szSmsTelcoInfo)); 
					memset(gSenderInfo.szSmsFlag, 0x00, sizeof(gSenderInfo.szSmsFlag)); 
					util.findValueParse(logonDbInfo.szReserve, "mms_tel" ,gSenderInfo.szSmsTelcoInfo); 
					util.findValueParse(logonDbInfo.szReserve, "mms_yn"	,gSenderInfo.szSmsFlag);
					util.findValueParse(logonDbInfo.szReserve,	"sell", gSenderInfo.szSellerFlag);
					util.findValueParse(logonDbInfo.szReserve,	"idcd", gSenderInfo.szIdCode);
					logPrintS(0,"[INF] info modify ok");
					break;

				case 6: //callback list reload
					checkCallback.loadCallbackList(processInfo, logonDbInfo);
					logPrintS(0,"[INF] callback info reload ok");
					break;

				case 7: //chk_callback state reload
					{
						memset(gSenderInfo.szChkCallback, 0x00, sizeof(gSenderInfo.szChkCallback));
						util.findValueParse(logonDbInfo.szReserve, "chk_callback", gSenderInfo.szChkCallback);
						int loadRet = 0;

						if(strcmp(gSenderInfo.szChkCallback, "1") == 0)
						{
							checkCallback.set_callback_list.clear();

							loadRet = g_oracle.selectTblCallback(checkCallback.set_callback_list, logonDbInfo.nmPID);
							if(loadRet < 0)
							{
								logPrintS(0, "loadCallbackList error[%s:%d]", __FILE__, __LINE__);
								return;
							}
						}   
						else if(strcmp(gSenderInfo.szChkCallback, "2") == 0)
						{
							loadRet = loadDialCodeAll();
							if(loadRet < 0)
							{
								logPrintS(0, "loadDialCodeAll error[%s:%d]", __FILE__, __LINE__);
								return;
							}
						}
						logPrintS(0, "[INF] chk_callback[%s] modified ok", gSenderInfo.szChkCallback );
					}  

					break;

				default:
					break;
			}
	
			time(&SThisT);	
			ret = (int)difftime(SThisT,monLastT);
			if( ret > 30 )
			{
				monitor.setDataSum(sum);
				monitor.setCurDate();
				monitor.send(gConf.monitorName);
				time(&monLastT);
				sum=0;
			}
			continue; // no data
		}
		 
		//logPrintS(1,"[STP] -3- []");
		
		// write log
		//로그 주석 처리.2011.11.23.
#ifdef _DEBUG
	    _monPrint(_MONILOG,(char*)hRemoteSock.getMsg());
	    printf("\n\n%s\n\n", (char*)hRemoteSock.getMsg());
#endif
			
#ifdef TIME
		/* gettimeofday */
		struct timeval timefirst, timesecond;
		struct timezone tzp;
		int secBuf, microsecBuf;
		float timeBuf;
		
		gettimeofday(&timefirst,&tzp);
		/* gettimeofday */
#endif
		//logPrintS(1,"nCurAccCnt(%d)",nCurAccCnt);
		
		ret = classifyS(monitor, processInfo, logonDbInfo, db, hRemoteSock);

		if( ret < 0 )
		{
			logPrintS(0,"[ERR] classifyS Error ret [%d]",ret);
			goto SenderEND;
		}
		//logPrintS(1,"[STP] -4- []");
			
#ifdef TIME
		gettimeofday(&timesecond,&tzp);
		secBuf 		= (timesecond.tv_sec - timefirst.tv_sec);
		microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
		timeBuf 	= microsecBuf;
		timeBuf 	= timeBuf / 1000000;
		timeBuf 	= timeBuf + secBuf;
		logPrintS(0,"senderProcess db time [%f]",timeBuf);
#endif
    }

SenderEND:
	logPrintS(0,"[INF] socket END sockfd[%d]CID[%s]", hRemoteSock.getSockfd(), logonDbInfo.szCID);
	hRemoteSock.close();
	
	return;
}


int SenderProcess::classifyS(CMonitor& monitor,CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo,CKSSocket& db,CKSSocket& hRemoteSock) 
{
	int ret = 0;
	char szYYYYMM[32];
	string strPacketHeader;
	
	CSenderDbMMSID senderDbMMSID;
	CBcastData bCastData;

	strPacketHeader = "";
	strPacketHeader.reserve(0);
			
	// 시간측정로그 : classifyS strPacketHeader.insert 시작
	struct tm *d;
	struct timeval val;
	// gettimeofday(&val, NULL);
	// d=localtime(&val.tv_sec);		
	//logPrintS(1,"4[classifyS strPacketHeader.insert Start]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
  
	strPacketHeader.insert(0,hRemoteSock.getMsg(),30);
  
	if( strstr(strPacketHeader.c_str(),"BEGIN PING\r\n") ) 
	{
		fflush(stdout);
		//logPrintS(0,"[INF] socket link recv");
		
		ret = sendPong(hRemoteSock);
		monitor.setLinkTime();
		if( ret < 0 ) 
		{
			return ret;
		}
	} 
	else if ( strstr(strPacketHeader.c_str(),"BEGIN MMSSEND\r\n") )  
	{

		/* 
		 * 20141210 운영팀 요청사항으로 
		 * 처리오류시 sendAck 전송시에는 종료 안함
		 */
		// 20140212 : mmsPacketSend.parse fix return 100
		/*
			return parse result
				-1 : MData Header info error
				 0 : OK
		*/
		logPrintS(1,"[DBG] -0- []");
		ret = mmsPacketSend.parse((char*)hRemoteSock.getMsg());
		if( ret != 100 ) 
		{
			logPrintS(0,"[ERR] packet parse failed - ErrMsg[%s]",mmsPacketSend.getErrorMsg());
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"Msg Parsing Error");
			//return ret;
			return 0;
		}

		//getMMSID2DB(db,senderDbMMSID,szSenderID);//디비 처리 하기
		/**====================================================================================**/
		//20140212 : ADD if 처리
		/**====================================================================================**/
		ret = getMMSID2DB(senderDbMMSID,szSenderID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get MMSID failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET MMS ID FAILED.");
			//return ret;
			return 0;
		}
		//getCTNID2DB(db,senderDbMMSID,szSenderID);// 디비 처리 하기
		/**====================================================================================**/
		//20140212 : ADD if 처리
		/**====================================================================================**/

		ret = getCTNID2DB(senderDbMMSID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get CTNID failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET CTN ID FAILED.");
			//return ret;
			return 0;
		}	
		/**====================================================================================**/
		writeLogMMSData(mmsPacketSend,senderDbMMSID.mmsid,senderDbMMSID.ctnid);// LOG write

		CMMSFileProcess mmsFileProcess;// write file object
 	
		memset(szYYYYMM	,0x00	,sizeof(szYYYYMM));  //CCL(szYYYYMM);
		get_timestring("%04d%02d",time(NULL),szYYYYMM);
		trim(szYYYYMM,strlen(szYYYYMM));


		//mmsFileProcess.write(db
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		ret = mmsFileProcess.write(db
              					,mmsPacketSend
              					,gConf.ContentPath
              					,senderDbMMSID.mmsid
              					,szYYYYMM
              					,szSenderID
              					,senderDbMMSID.ctnid
              					,gConf.dbRequestTimeOut
              					,senderDbDomainName
              					);
		

		//		ret = -1;		//test code
		if(ret < 0 )
		{
			logPrintS(0,"[ERR] file write failed [%d]", ret);
			/*
			 * ret == -2 : IMG 사이즈 1M 초과 오류
			 */
			if ( ret == -2)
				sendAck(hRemoteSock,mmsPacketSend,2104,senderDbMMSID.ctnid ,"컨텐츠 크기 초과");
			else
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"FILE WRITE FAILED.");
			//return ret;
			return 0;
		}
		/**====================================================================================**/
		//logPrintS(1,"[DBG] -1- [%lld] : File write end",senderDbMMSID.mmsid);
	

		//setMMSCTNTBL2DB(db,mmsFileProcess);// insert CTL
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		ret = setMMSCTNTBL2DB(mmsFileProcess);
   		if ( ret < 0)
		{
			logPrintS(0,"[ERR] db insert table content failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT CONTENT TABLE FAILED.");
			//return ret;
			return 0;
		}	
		/**====================================================================================**/
		//logPrintS(1,"[DBG] -2- [%lld]",senderDbMMSID.mmsid);
		//setMMSTBL2DB(db,senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT);  // send table
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		ret = setMMSTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT);  // send table
		if ( ret < 0 )
		{
			if (strlen(mmsPacketSend.getSenderValue()) == 0)
			{
				logPrintS(0,"[ERR] 발신번호 오류[%s]", mmsPacketSend.getSenderValue());
				sendAck(hRemoteSock,mmsPacketSend,2106,senderDbMMSID.ctnid ,"발신번호 오류");
			}
			else
			{
				logPrintS(0,"[ERR] db insert table MMSTBL failed");
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT MMSTBL FAILED.");
			}
			//return ret;
			return 0;
		}
	
		//번호 변작 방지 로직		
		if(strncmp(gSenderInfo.szChkCallback, "1", 1) == 0) 
		{
			if(checkCallback.findCallback(mmsPacketSend.getSenderValue()) < 0)
			{
				logPrintS(0, "[ERR] [%s]회신 번호 리스트에 존재하지 않습니다.", mmsPacketSend.getSenderValue());
				ret = setRPTTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.ctnid, logonDbInfo.nmPRT, 50, "NO CALLBACK INFO");
				sendAck(hRemoteSock, mmsPacketSend, 50, senderDbMMSID.ctnid, "NO CALLBACK INFO");
				return 0;
			}
		}
		else if(strncmp(gSenderInfo.szChkCallback, "2", 1) == 0)
		{
			if(checkCallback.examineCallback(mmsPacketSend.getSenderValue()) < 0)
			{
				logPrintS(0, "[ERR] [%s]", checkCallback.get_errorMsg().c_str());
				ret = setRPTTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT, 40, "CALLBACK IS NOT VALID");
				sendAck(hRemoteSock, mmsPacketSend, 40, senderDbMMSID.ctnid, "CALLBACK IS NOT VALID");
				return 0;
			}
		}

		//logPrintS(1,"[DBG] -3- [%lld]",senderDbMMSID.mmsid);
		/**====================================================================================**/
		/*
			send queue
			1. 원본.2012.10.09 setMMSMSG2DB(db,senderDbMMSID.mmsid, senderDbMMSID.ctnid, mmsFileProcess,logonDbInfo.nmPRT);
			2. CID 값 추가.2012.10.09
		*/
		//setMMSMSG2DB(db, senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.ctnid, mmsFileProcess,logonDbInfo.nmPRT);
		/**====================================================================================**/
		//20140212 ADD if 
		/**====================================================================================**/
		ret = setMMSMSG2DB(senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.ctnid, mmsFileProcess,logonDbInfo.nmPRT);
		
		if ( ret < 0 )
		{
			logPrintS(0,"[ERR] db insert MMS MSG failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT MMS MSG FAILED.");
			//return ret;
			return 0;
		}
		//logPrintS(1,"[DBG] -4- [%lld]",senderDbMMSID.mmsid);
		
		/**====================================================================================**/
		/*
		 *	SendAck 부분을 밑에서 위로 옮김.2012.08.20
		 *	동보 전송일 경우 레포트가 먼저 수신되는 경우가 생겨 옮김
		 */
		ret =  sendAck(hRemoteSock,mmsPacketSend,ret, senderDbMMSID.ctnid ,"Succ");
		if ( ret  < 0 )
		{
			logPrintS(0,"[ERR] socket send ack failed");
		}
		logPrintS(1,"[DBG] -5- [%lld] : Send Ack End",senderDbMMSID.mmsid);
		
		/**====================================================================================**/
		// check bcast~
		int nBcastRet;
		nBcastRet = mmsPacketSend.getBcastFirst(bCastData);
		while( nBcastRet == 0 ) 
		{ 
			nCurAccCnt++;
	            
			//getMMSID2DB(db,senderDbMMSID,szSenderID);
			/**====================================================================================**/
			//20140212 ADD if 
			/**====================================================================================**/
			ret = getMMSID2DB(senderDbMMSID,szSenderID);
			if(ret < 0)
			{
				logPrintS(0,"[ERR] db select BCAST get MMSID failed");
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET BCAST MMSID FAILED.");
				//return ret;
				return 0;
			}	
			/**====================================================================================**/
			logPrintS(0,"[INF] socket_domain get senderDB - bcast MMSID[%lld] ", senderDbMMSID.mmsid);
	
			//mmsFileProcess.write(db 
			/**====================================================================================**/
			//20140212 ADD if
			/**====================================================================================**/
			ret = mmsFileProcess.write(db 
			                    ,mmsPacketSend
			                    ,gConf.ContentPath
			                    ,senderDbMMSID.mmsid
			                    ,szYYYYMM
			                    ,szSenderID
			                    ,senderDbMMSID.ctnid
			                    ,gConf.dbRequestTimeOut
			                    ,senderDbDomainName
			                    ,true
			                    );
			if( ret < 0 )
			{
				logPrintS(0,"[ERR] file write bcast failed");
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"FILE WRITE BCAST FAILED.");
				//return ret;
				return 0;
			}
			/**====================================================================================**/


			//setMMSTBL2DB(db,senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT,&bCastData);
			/**====================================================================================**/
			//20140212 ADD if
			/**====================================================================================**/
			ret = setMMSTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT,&bCastData);
			if( ret < 0 )
			{
				logPrintS(0,"[ERR] db insert bcast table MMSTBL failed");
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT BCAST MMSTBL FAILED.");
				//return ret;
				return 0;
			}
			
			/**====================================================================================**/
			//setMMSMSG2DB(db,senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.ctnid,mmsFileProcess,logonDbInfo.nmPRT,&bCastData);
			/**====================================================================================**/
			//20140212 ADD if	
			/**====================================================================================**/
			ret = setMMSMSG2DB(senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.ctnid,mmsFileProcess,logonDbInfo.nmPRT,&bCastData);
			if( ret < 0 )
			{
				logPrintS(0,"[ERR] db insert bcast MMS MSG failed");
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT BCAST MMS MSG FAILED.");
				//return ret;
				return 0;
			}
			/**====================================================================================**/
			logPrintS(1,"[INF] bcast send mmsid[%lld]ctnid[%d]seq[%s]key[%s]extend[%s]dst[%s]"
								,senderDbMMSID.mmsid
								,senderDbMMSID.ctnid
								,bCastData.strSeq.c_str()
								,bCastData.strKey.c_str()
								,bCastData.strExtend.c_str()
								,bCastData.strReceiver.c_str()
								);
	
			nBcastRet = mmsPacketSend.getBcastNext(bCastData);
		}
//-----------------------------------------------------------------------------
		monitor.setDataTime();    
		nCurAccCnt++;
        
		ret =	SenderLimit(logonDbInfo);
		
		if(ret == -1)
		{
			logPrintS(0,"[ERR] limit SendreLimit ret [%d]",ret);
			return -1;
		}		//	logPrintS(1,"ret(%d)nCurAccCnt(%d)",ret,nCurAccCnt);
		//logPrintS(1,"[DBG] -6- [%lld]",senderDbMMSID.mmsid);

	} 
	else  // error
	{
		logPrintS(0,"[INF] invalid msg header data - [%s] ", strPacketHeader.c_str());

		fflush(stdout);
		ret = -1;
	}

	return ret;
}								


int SenderProcess::SenderLimit(CLogonDbInfo& logonDbInfo)
{
	int ret;
	char szTemp[256];
	
	//* < brief 발송 제한 체크
	if (atoi(logonDbInfo.szLimitType) != 0)
	{
		memset(szTemp	,0x00	,sizeof(szTemp));
	
		get_timestring("%04d%02d%02d%2d%2d%2d", time(NULL), szLimitCurTime);	// 현재 날짜 구하기
		
		if (strcmp(szLimitTime,"") == 0)
		{
			strcpy(szLimitTime,szLimitCurTime);	// 년월일 값 구하기
		}

		ret = LimitCheck(logonDbInfo);
		//카운트 계산에 따른 서비스 제한 및 알림 기능 수행
		// logPrintS(1,"ret(%d)",ret);
		switch (ret)
		{
			case 9 : // 일 변경에 따른 누적 카운트 초기화 및 프로시저 실행
			case 10 : // 월 변경에 따른 누적 카운트 초기화 및 프로시저 실행
				if (ret == 9)
					logPrintS(1,"[INF]day change total count reset and run");
				if (ret == 10)
					logPrintS(1,"[INF]month change total count reset and run");
	
				//발송 제한 변수 초기화
				bDayWarnCheck = false;
				bMonWarnCheck = false;
				
				if (ret == 9)
					logonDbInfo.nMonAccCnt += logonDbInfo.nCurAccCnt;	//일 변경 시 월 카운트는 누적
				if (ret == 10)
					logonDbInfo.nMonAccCnt = 0;	//월 변경시 월 카운트는 초기화
					
				logonDbInfo.nDayAccCnt = 0;	//일,월 변경시 일 카운트는 항상 초기화
				nCurAccCnt = 0;	//일,월 변경시 현재 누적 카운트는 항상 초기화
				memset(szLimitTime,(char)NULL,sizeof(char)*16);
				break;
			default :
				break;
		}
				
		switch (ret)
		{
			case 0 : // 변경 없음
				break;
			case 1 : // 일 서비스 제한
				logPrintS(1,"[INF] daily limit [%d]"	,logonDbInfo.nDayLimitCnt);
				return -1;
			case 2 : // 월 서비스 제한
				logPrintS(1,"[INF] monthly limit [%d]"	,logonDbInfo.nMonLimitCnt);
				return -1;
			case 3 : // 일 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] daily limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(1,"[%s]"	,szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				return -1;
			case 4 : // 월 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] monthly limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(1,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				return -1;
			case 5 : // 일 알람
				sprintf(szTemp,"[INF] daily limit orver - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(1,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				break;
			case 6 : // 월 알람
				sprintf(szTemp,"[INF] monthly limit over - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(1,"%s",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				break;
			case 7 : // 일 임계치 알람
				if (!bDayWarnCheck)
				{
					bDayWarnCheck = true;
					sprintf(szTemp,"[INF] daily limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nDayWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin daily limit msg send failed",0,0);
					}
						
				}
				break;
			case 8 : // 월 임계치 알람
				if (!bMonWarnCheck)
				{
					bMonWarnCheck = true;
					sprintf(szTemp,"[INF] monthly limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nMonWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin monthly limit msg send failed",0,0);
					}
						
				}
				break;
			default :
				break;
		}
	}
	
	return 0;
}


//* < brief 발송 제한 체크
int SenderProcess::LimitCheck(CLogonDbInfo& logonDbInfo)
{
/* return value
	1 : 일 서비스 제한
	2 : 월 서비스 제한
	3 : 일 서비스 제한	+ 알람
	4 : 월 서비스 제한	+ 알람
	5 : 일 알람
	6 : 월 알람
	7 : 일 임계치 알람
	8 : 월 임계치 알람
	9 : 일 변경에 따른 누적 카운트 초기화
	10 : 월 변경에 따른 누적 카운트 초기화
	0 : 변경 없음
*/
	bool bDay=false;
	bool bMon=false;
	int  nDayAccCnt 		= logonDbInfo.nDayAccCnt;
	int  nMonAccCnt 		= logonDbInfo.nMonAccCnt;
	int  nDayWarnCnt 		= logonDbInfo.nDayWarnCnt;
	int  nMonWarnCnt 		= logonDbInfo.nMonWarnCnt;
	int  nDayLimitCnt 		= logonDbInfo.nDayLimitCnt;
	int  nMonLimitCnt 		= logonDbInfo.nMonLimitCnt;
	int  nLimitType 		= atoi(logonDbInfo.szLimitType);
	int  nLimitFlag 		= atoi(logonDbInfo.szLimitFlag);

	//logPrintS(1,"szLimitCurTime(%s)szLimitTime(%s)",szLimitCurTime,szLimitTime);
	if (strncmp(szLimitTime	,szLimitCurTime	,8) != 0)
		bDay = true;	//일 단위
		
	if (strncmp(szLimitTime,szLimitCurTime,6) != 0) 
		bMon = true;	//월 단위

	if (bDay)
	{
		if (bMon)
		{
			return 10;	// 월 변경이 이루어 졌을 경우
		}
		else
		{
			return 9;	// 일 변경이 이루어 졌을 경우
		}
	}
 //logPrintS(1,"nLimitType(%d),nDayWarnCnt(%d),nCurAccCnt(%d),nDayAccCnt(%d)",nLimitType, nDayWarnCnt, nCurAccCnt,nDayAccCnt);
	//서비스 제한 체크
	switch (nLimitType)
	{
		case 0:	//발송 제한 적용 안함
			return 0;
		case 1:	//일,월 발송 제한
		case 2:	//일 발송 제한
		case 3:	// 월 발송 제한
			if (nLimitType == 1 || nLimitType == 2)
			{
				//일 임계치 체크 (임계치와 제한 건수 사이)
				if (((nDayAccCnt + nCurAccCnt) > nDayWarnCnt) && ((nDayAccCnt + nCurAccCnt) < nDayLimitCnt))
				{
					logPrintS(1,"[INF] daily limit - limit over [%d/%d]", nDayWarnCnt, (nDayAccCnt + nCurAccCnt)-nDayWarnCnt);
					return 7;
				}
				//일 서비스 제한 체크
				if ((nDayAccCnt + nCurAccCnt) > nDayLimitCnt)
				{
					logPrintS(1,"[INF] daily limit - config value [%d]", nDayLimitCnt);
				
					switch (nLimitFlag)
					{
						case 1 :
							return 1;
						case 2 :
							return 3;
						case 3 :
							return 5;
						default :
							return 0;
					}
				}
			}
//logPrintS(1,"nMonWarnCnt(%d),nMonAccCnt(%d),nMonLimitCnt(%d)",nMonWarnCnt, nMonAccCnt+nCurAccCnt+nDayAccCnt, nMonLimitCnt);

			if (nLimitType == 1 || nLimitType == 3)
			{
				//월 임계치 체크 (임계치와 제한 건수 사이)
				if (((nMonAccCnt + nCurAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt) < nMonLimitCnt))
				{
					logPrintS(1,"[INF] monthly limit - limit over [%d/%d]", nMonWarnCnt, (nMonAccCnt + nCurAccCnt)-nMonWarnCnt);
					return 8;
				}
				//월 서비스 제한 체크
				//	if ((nMonAccCnt + nCurAccCnt) > nMonLimitCnt)
				if ((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonLimitCnt)
				{
					logPrintS(1,"[INF] monthly limit - config value [%d]", nMonLimitCnt);
					switch (nLimitFlag)
					{
						case 1 :
							return 2;
						case 2 :
							return 4;
						case 3 :
							return 6;
						default :
							return 0;
					}
				}
			}
		default:
			return 0;
		break;
	}
	return 0;
}

int loadDialCodeAll()
{
	checkCallback.set_dialcode_list_0101.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0101, (char*)"0101") < 0)
		return -1;

	checkCallback.set_dialcode_list_0102.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0102, (char*)"0102") < 0)
		return -1;

	checkCallback.set_dialcode_list_0103.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0103, (char*)"0103") < 0)
		return -1;

	checkCallback.set_dialcode_list_0104.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0104, (char*)"0104") < 0)
		return -1;

}
