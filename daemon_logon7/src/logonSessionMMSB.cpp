#include "logonSessionMMSB_PART.h"
#define OVERTIME 1000
#define		RECV_SELECT_TIME	100000

#include    <time.h>
#include <unistd.h>


typedef struct _THREAD_PARAM {
	int sockfd;
	struct in_addr inaddr;
	pthread_t tid;
} ThreadParam;

void*   doService(void* param);
int startProcess(char* szConfigFile);
char *matchString(char* szOrg, char* szTag, string& strVal);

char szSenderID[16];

int main(int argc, char* argv[])
{
    int ret;
    int hOpenSocket;
    char logMsg[1024];
    int clisock = 0 ;
    socklen_t clilen=0;
    struct sockaddr_in cli_addr;
    struct timeval outtime;
    fd_set rset;
    char szIP[16];
    int nPort;
	ThreadParam* param;
	int hNewSocket = 0 ; /** new connection socket */

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }


    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        ml_sub_end();
        exit(1);
    }

    printf("[%s][%d][%d][%s]\n",
            gConf.serverIP,
            gConf.serverPORT,
            gConf.process_sleep_time,
            gConf.logonDBName);


    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server_Fork();

    CCL(szIP);


    strcpy(szIP,gConf.serverIP);
    nPort = gConf.serverPORT;

    /* socket open */
    ret=UnixSockOpenNon(&hOpenSocket,szIP,nPort);
    if ( ret!=0 ) {

	sprintf(logMsg,"SOCKET OPEN ERROR [%s]", strerror(errno));
        monitoring(logMsg,0,0);
        ml_sub_end();
        if ( hOpenSocket ) close( hOpenSocket );
        return -1;
    }

    while(activeProcess)
    {
        wait_a_moment(gConf.process_sleep_time);
        outtime.tv_sec = 0;
        outtime.tv_usec = 50000;
        FD_ZERO(&rset);
        FD_SET(hOpenSocket,&rset);
        ret = select(hOpenSocket+1, (fd_set*)&rset, (fd_set*)NULL, (fd_set*)NULL, &outtime);
        if ( ret>0 ) {

			clilen = sizeof(cli_addr);
			hNewSocket = accept(hOpenSocket, (struct sockaddr*)&cli_addr, &clilen);
			if( hNewSocket < 0 )   {
				log_history(0,0,"Accept Client Socket Error[%s]",strerror(errno));
				return -1;
			}
			else if (hNewSocket > 0) {
				
				log_history(0,0,"connect [%s]",inet_ntoa(cli_addr.sin_addr));

            
			/*
			getAccept(hOpenSocket,szIP,nPort);
			*/

				param = (ThreadParam*)malloc(sizeof(ThreadParam));
				memset( param, 0x00, sizeof(ThreadParam) );
				param->sockfd = hNewSocket;
				memcpy(&param->inaddr, &cli_addr.sin_addr, sizeof(struct in_addr));


				/* thread Create */
				if( pthread_create(&param->tid, NULL, doService, (void*)param) ) {
					log_history(0,0,"THREAD CREATE ERROR.[%d][%s]",0,errno);
					close(hNewSocket);
				}

			}

        }
 
    }

    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    if( hOpenSocket) close(hOpenSocket);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}

void* doService(void* param)
{
    int hNewSocket = 0 ; /** new connection socket */
    socklen_t clilen=0; /** new connection socket size */
    struct sockaddr_in cli_addr; /** new connection info struct */
    struct timeval outtime;
    fd_set rset;
	int ret;
	
	CKSSocket newSockfd;
    CKSSocket db;			/** senderDB domain Socket	*/
    CMMSPacketSend mmsPacketSend;
	char header[65];
	char buff[4096];
	char body[4096];
	char ack[100];

	char ErrBuf[512];
	char logMsg[4096];

	string strKey;

	FILE *      oFD;
	char        file[256], date[32], buf[256];
	

	int total_size = 0;
	time_t	LastT, ThisT;
	fd_set readfds;
	fd_set tempfds;

	ThreadParam* prop = (ThreadParam*)param;
	pthread_detach(pthread_self());

    pid_t childPid;
    char szKillCmd[32];
	int nReadData = 0;
	int bodySize = 0;
	int retSel = 0;
	int ack_len = 0;


	CCL(logMsg);
	sprintf(logMsg,"THREAD_INIT:[%s][%d]",inet_ntoa(prop->inaddr),prop->sockfd);
	monitoring(logMsg,0,0);

	hNewSocket = prop->sockfd;
	newSockfd.attach(hNewSocket);

	time(&LastT);
	while(activeProcess)
	{
		outtime.tv_sec = 0;
		outtime.tv_usec = RECV_SELECT_TIME;
				
		FD_ZERO(&readfds);
		FD_SET(prop->sockfd,&tempfds);
		FD_SET(prop->sockfd,&readfds);
		
        wait_a_moment(1000);	//0.001
		retSel = select(prop->sockfd+1, (fd_set*)&readfds, (fd_set*)0, (fd_set*)0, &outtime);
log_history(0,0,"[%d]:select end", hNewSocket);
		if (retSel>0) {
			
			time(&LastT);
			memset(buff,0x00,sizeof(buff));
			
			//ret = newSockfd.recv(buff,4);
			ret = read(hNewSocket,buff,4);
			if( ret == 0 )
			{
				log_history(0,0,"[%d]:size recv client close", hNewSocket);
				break;

			}
			if( ret < 0 )
			{
				log_history(0,0,"[%d]:size recv Error", hNewSocket);
				break;
			}

			//log_history(0,0,"read sizeinfo");
			//viewPack(buff,ret);

			nReadData = atoi(trim(buff,sizeof(buff)));
			
			log_history(0,0,"[%d]:read sizeinfo : %d", hNewSocket, nReadData);
			
			memset(header,0x00,sizeof(header));
			ret = newSockfd.recv(header,64);
			if( ret == 0 )
			{
				log_history(0,0,"[%d]:header recv client close", hNewSocket);
				break;
			}
			if( ret < 0 )
			{
				log_history(0,0,"[%d]:hreader recv Error", hNewSocket);
				break;
			}

			log_history(0,0,"[%d]:read header : %s[%d]", hNewSocket, header, ret);
			viewPack(header,ret);
			//[BCCARD   ETC9630BCCARD   ...20091116181317  84                  ]
			// 123456789___123412345678901212345678901234__12123456789012345678
			nReadData = nReadData - 64;

			total_size = 0;
			bodySize = nReadData;
			
			memset(body,0x00,sizeof(body));
			while(1) {
				if(total_size >= bodySize ) break;

				memset(buff,0x00,sizeof(buff));
				ret = newSockfd.recv(buff,nReadData);
				if( ret == 0 )
				{
					log_history(0,0,"[%d]:body recv client close", hNewSocket);
					break;
				}
				if( ret < 0 )
				{
					log_history(0,0,"[%d]:body recv Error", hNewSocket);
					break;
				}

				//viewPack(buff,ret);

				memcpy(body+strlen(body),buff,ret);
				total_size += ret;
				nReadData = bodySize - total_size;
				log_history(0,0,"[%d]:recv size : [%d]", hNewSocket, ret);
			}
			
			//log_history(0,0,"read body END");
			//viewPack(body,total_size);
			log_history(0,0,"[%d]:read body END", hNewSocket);
			
//			memset(date,0x00,sizeof(date));
//			get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), date);
//
//			date[14]= 0x00;
//			date[15]= 0x00;
//
//			strcpy(file,"/user/neomms/CNT/bcshop/PROCESS_NAME");
//
//			strcat(file,date);
//			if ((oFD = fopen(file, "w")) == NULL)
//			{
//				log_history(0,0,"log file open error[%s][%s]",file,strerror(errno));
//
//			}
//			//log_history(0,0,"log file[%s]",file);
//			fprintf(oFD,"%s",body);
//			fclose(oFD);
//			log_history(0,0,"log file[%s]",file);
			
			//log_history(0,0,"read body END");
			
			char recvKey[64];

			memset(recvKey,0x00,sizeof(recvKey));
			matchString(body,"KEY",strKey);
			strcpy(recvKey,(char*)strKey.c_str());

			log_history(0,0,"[%d]:FIND KEY [%s]", hNewSocket,recvKey );

			// BCCARD   ETC9630BCCARD   ...20091116190109  84                  
			// BCCARD   ETC9631BCCARD   
			memcpy(header+12,"9651",4);
			memcpy(header+42,"00",2);

			memset(buff,0x00,sizeof(buff));

			memset(ack,0x00,sizeof(ack));
			sprintf(ack,"BEGIN ACK\r\nKEY:%s\r\nCODE:100\r\nDESC:\r\nEND\r\n",recvKey);
			//strcpy(ack,"BEGIN ACK\r\nKEY:1234\r\nCODE:100\r\nDESC:\r\nEND");
			ack_len = strlen(ack);
			sprintf(buff,"%04d",ack_len+64);
			log_history(0, 0, "[%d]:ACK[%s]", hNewSocket, ack);
			memcpy(&buff[4], header, 64);


			memcpy(&buff[4+64], ack, ack_len);
			buff[4+64+ack_len] = 0;
			/*
			strcpy(buff,"0113");
			*/
			//ret = newSockfd.send(buff,4);
			ret = newSockfd.send(buff,4+64+ack_len);
			if( ret == 0 )
			{
				log_history(0,0,"[%d]:ack size write client close", hNewSocket);
				break;
			}
			if( ret < 0 )
			{
				log_history(0,0,"[%d]:ack size write Error[%d][%d][%s]", hNewSocket, ret, errno, strerror(errno));
				break;
			}

			log_history(0,0,"[%d]:send size[%d][%s][%s][%s]", hNewSocket,ret, buff, &buff[68], recvKey);

		    ret = classifyS(db, body, mmsPacketSend);
		    if( ret < 0 )
		        continue;
		        
			/*
			startProcess(file);
			*/

/*
			int child_pid;
			char tmpPath[512];
			char execProcessName[512];
			char tmpname[512];
			strcpy(tmpname,"bshopsender");
			strcpy(execProcessName,"/user/www/work/bshopsend.php");

			log_history(0,0,"startProcess fork START[%s]",file);

			if( (child_pid = fork()) == 0 )
			{
				// newSockfd.close();
				memset(tmpPath,0x00,sizeof(tmpPath));
				sprintf(tmpPath,"/user/www/work/");
				if (chdir(tmpPath) < 0)
				{
					log_history(0,0,"[startProcess] %s chdir error.[%s]",tmpPath,strerror(errno));
					exit(0);
				}
				if (execlp(execProcessName,tmpname,file , (char*)0)  < 0 )
				{
					log_history(0,0,"[startProcess] %s execlp error.<%s>",
							execProcessName, strerror(errno));
					exit(0);
				}
				//sleep(3);
				//log_history(0,0,"startProcess exit[%s]",file);
				exit(0);
			} else if( child_pid < 0 ) {
				log_history(0,0,"startProcess Error [%s]",strerror(errno));
			} 
			//sleep(3);
			//sleep(1);
			log_history(0,0,"startProcess fork END[%s]",file);
*/
			
		} else if (retSel<0) {
		    CCL(logMsg);
		    CCL(ErrBuf);
		    sprintf(logMsg,"[%d]:retSel[%d][%s]", hNewSocket,retSel, strerror_r(errno,ErrBuf,sizeof ErrBuf));
		    monitoring(logMsg,0,0);
		    
			/* EINTR 인경우 정상 처리 */
			if (errno != EINTR)
			{
				break;
			}
		}

		time(&ThisT);
		if ( difftime(ThisT,LastT)>=OVERTIME ) {	/* 일정시간이 되면 강제종료 */
		    CCL(logMsg);
		    sprintf(logMsg,"[%d]:규정시간[%d sec] Over로 강제 종료함.", hNewSocket,
		        OVERTIME);
			monitoring(logMsg,0,0);
			break;
		}

    }

    CCL(logMsg);	
    sprintf(logMsg,"[%d]:THREAD  OUT:[%s]", hNewSocket,inet_ntoa(prop->inaddr));
	monitoring(logMsg,0,0);
	newSockfd.close();
	free(prop);
	 return 0;
}


/** @return 음수 일시 프로세스 종료
*/
int classifyS(
        CKSSocket& db,
        char* buff, 
        CMMSPacketSend& mmsPacketSend)
{
    int ret=0;
    char szYYYYMM[32];
    CSenderDbMMSID senderDbMMSID;
    CBcastData bCastData;

	ret = mmsPacketSend.parse(buff);
	if( ret != 100 ) {
		log_history(0,0, "packet parse Error [%s]",mmsPacketSend.getErrorMsg());
		return ret;
	}
//-----------------------------------------------------------------------------
    // 디비 처리 하기
    
    strcpy(szSenderID,mmsPacketSend.getIdValue());
    getMMSID2DB(db, senderDbMMSID, szSenderID);
    getCTNID2DB(db, senderDbMMSID, szSenderID, mmsPacketSend);
	log_history(0,0, "getMMSID [%d], get ctnid [%d] szSenderID[%s]",senderDbMMSID.mmsid, senderDbMMSID.ctnid, szSenderID);
//-----------------------------------------------------------------------------
    writeLogMMSData(mmsPacketSend,
            senderDbMMSID.mmsid,
            senderDbMMSID.ctnid);
//-----------------------------------------------------------------------------
    // write file
    CMMSFileProcess mmsFileProcess;
    CCL(szYYYYMM);
    get_timestring("%04d%02d",time(NULL),szYYYYMM);
    trim(szYYYYMM,strlen(szYYYYMM));
    mmsFileProcess.write(
            db,
            mmsPacketSend,
            gConf.ContentPath,
            senderDbMMSID.mmsid,
            szYYYYMM,szSenderID,
            senderDbMMSID.ctnid,
            gConf.dbRequestTimeOut,
            gConf.senderDBName);
//-----------------------------------------------------------------------------
// insert CTL
    int sRet=0;
    setMMSCTNTBL2DB(db,mmsFileProcess);
    sRet = setMMSTBL2DB(db, senderDbMMSID.mmsid, senderDbMMSID.ctnid,1, mmsPacketSend);
	setMMSMSG2DB(db, senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.ctnid, mmsFileProcess, 1,sRet, mmsPacketSend);
//-----------------------------------------------------------------------------
     
    return ret;

}

int getCTNID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,char* cid, CMMSPacketSend& mmsPacketSend)
{
	if(mmsPacketSend.getImgCnt() == 0){
		senderDbMMSID.ctnid = -1;
		return 0;
	}
		
    CSenderDbInfoAck senderDbInfoAck;
    CDBUtil dbUtil;
    senderDbMMSID.header.type = GETCTNID;
    senderDbMMSID.header.leng = sizeof(CSenderDbMMSID) - sizeof(Header);
    senderDbMMSID.ctnid = 0;
    memcpy(senderDbMMSID.szCid,cid,10);
	senderDbInfoAck.ctnid = 0;

    dbUtil.sendQuery(
            db,
            (void*)&senderDbMMSID,
            (void*)&senderDbInfoAck,
            gConf.dbRequestTimeOut,
            gConf.senderDBName);

//    senderDbMMSID.mmsid = senderDbInfoAck.mmsid;
    senderDbMMSID.ctnid = senderDbInfoAck.ctnid;

	if(senderDbInfoAck.ctnid == 0 || senderDbInfoAck.ctnid < 0){
    	/*
    	 * 오류시 CTNID자동생성
    	 * CTNID 9자리
    	 * 9HHMISSSS 생성
    	 */
		struct timespec tmv;
		struct tm	tp;
		char	szTime[24+1];
		char	szCtnid[9+1];
		
		
		clock_gettime(CLOCK_REALTIME, &tmv);
		localtime_r(&tmv.tv_sec, &tp);
		sprintf(szTime, "9%02d%02d%02d%02d",		
			tp.tm_hour, tp.tm_min, tp.tm_sec,
			(int)tmv.tv_nsec
			);
    	//senderDbMMSID.ctnid = 999999999;
    	sprintf(szCtnid, "%.9s", szTime);
    	senderDbMMSID.ctnid = atoi(szCtnid);
    	log_history(0,0, "[ERROR getCTNID2DB] ctnid[%d] dbUtil_getErrorMsg[%s]", senderDbMMSID.ctnid, dbUtil.getErrorMsg());
    }
    return 0;
}


int getMMSID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID, char* cid)
{
    CSenderDbInfoAck senderDbInfoAck;
    CDBUtil dbUtil;
	
    senderDbMMSID.header.type = GETMMSID;
    senderDbMMSID.header.leng = sizeof(CSenderDbMMSID) - sizeof(Header);
    senderDbMMSID.mmsid = 0;
    memcpy(senderDbMMSID.szCid,cid,10);
	senderDbInfoAck.mmsid = 0;

    dbUtil.sendQuery(
            db,
            (void*)&senderDbMMSID,
            (void*)&senderDbInfoAck,
            gConf.dbRequestTimeOut,
            gConf.senderDBName);

    senderDbMMSID.mmsid = senderDbInfoAck.mmsid;
//    senderDbMMSID.ctnid = senderDbInfoAck.ctnid;

	if (senderDbInfoAck.mmsid == 0 || senderDbInfoAck.mmsid < 0){
    	/*
    	 * 오류시 MMSID자동생성
    	 * MMSID 9자리
    	 * 9HHMISSSS 생성
    	 */
		struct timespec tmv;
		struct tm	tp;
		char	szTime[24+1];
		char	szMmsid[9+1];
		
		clock_gettime(CLOCK_REALTIME, &tmv);
		localtime_r(&tmv.tv_sec, &tp);
		sprintf(szTime, "9%02d%02d%02d%02d",		
			tp.tm_hour, tp.tm_min, tp.tm_sec,
			(int)tmv.tv_nsec
			);
    	//senderDbMMSID.mmsid = 999999999;
    	sprintf(szMmsid, "%.9s", szTime);
    	senderDbMMSID.mmsid = atoi(szMmsid);
		log_history(0,0, "[ERROR getMMSID2DB] mmsid[%d] dbUtil_getErrorMsg[%s]", senderDbMMSID.mmsid, dbUtil.getErrorMsg());
    }

    return 0;
}

int setMMSCTNTBL2DB(CKSSocket& db,CMMSFileProcess& mmsFileProcess)
{
    int ret;
    time_t ThisT,LastT;
    CSenderDbMMSCTNTBL senderDbMMSCTNTBL;
    time(&LastT);
    CSenderDbInfoAck senderDbInfoAck;

    CMMSCtnTbl mmsCtnTbl;
    CDBUtil dbUtil;

    ret = mmsFileProcess.getMMSCtnTblFirst(mmsCtnTbl);
    if( ret != 0 ) return -1;

    while(ret == 0 )
    {

        memset(&senderDbMMSCTNTBL,0x00,sizeof(CSenderDbMMSCTNTBL));
        senderDbMMSCTNTBL.header.type = SETSENDCTNTBL;
        senderDbMMSCTNTBL.header.leng = sizeof(CSenderDbMMSCTNTBL) - sizeof(Header);


        senderDbMMSCTNTBL.nCtnId = mmsCtnTbl.nCtnId;
        memcpy(senderDbMMSCTNTBL.szCtnName,mmsCtnTbl.szCtnName,sizeof(mmsCtnTbl.szCtnName));
        memcpy(senderDbMMSCTNTBL.szCtnMime,mmsCtnTbl.szCtnMime,sizeof(mmsCtnTbl.szCtnMime));
        senderDbMMSCTNTBL.nCtnSeq = mmsCtnTbl.nCtnSeq;
        memcpy(senderDbMMSCTNTBL.szCtnSvc, mmsCtnTbl.szCtnSvc,sizeof(mmsCtnTbl.szCtnSvc));

        dbUtil.sendQuery(
                db,
                (void*)&senderDbMMSCTNTBL,
                (void*)&senderDbInfoAck,
                gConf.dbRequestTimeOut,
                gConf.senderDBName);
        ret = mmsFileProcess.getMMSCtnTblNext(mmsCtnTbl);
    }

    return 0;
}

int setMMSTBL2DB(CKSSocket& db,int nMMSId, int ctnid,
        int priority, CMMSPacketSend& mmsPacketSend,
        CBcastData * pBcastData)
{
    int ret = 0;
    time_t ThisT,LastT;
    CSenderDbMMSTBL senderDbMMSTBL;
    time(&LastT);

    memset(&senderDbMMSTBL,0x00,sizeof(CSenderDbMMSTBL));
    senderDbMMSTBL.header.type = SETSENDTBL;
    senderDbMMSTBL.header.leng = sizeof(CSenderDbMMSTBL) - sizeof(Header);
    strcpy(senderDbMMSTBL.szCallBack,mmsPacketSend.getSenderValue());



    if( pBcastData == NULL )
    strcpy(senderDbMMSTBL.szDstAddr,mmsPacketSend.getReceiverValue());
    else
    strcpy(senderDbMMSTBL.szDstAddr,pBcastData->strReceiver.c_str());

    strcpy(senderDbMMSTBL.szMsgTitle,mmsPacketSend.getSubjectValue());



    if( pBcastData == NULL )
    strcpy(senderDbMMSTBL.szPtnSn,mmsPacketSend.getKeyValue());
    else
    strcpy(senderDbMMSTBL.szPtnSn,pBcastData->strKey.c_str());


	/*
	 * TODO: 실제 szExtend데이터를  (setMMSMSG2DB)AQ전송처리시 데이터를 넘겨주어 한번에 처리하여야함 
	 *       운영중인 상황이라 데이터를 추가하지 못하여 복잡하게 반복처리 함
	 *       추후에는  szExtend데이터 추가하여 한번에 처리하게 해야함
	 * 1. 파트너스 실시간 데이터시 
	 * EXTEND 항목에 업무구분 '74?' 포함되어있음
	 * 실제 테이블 등록시는 제거하여 등록
	 * 2. 수신거부 목록 조회 
	 * 수신거부 조건 : 업무코드 "743" 일경우 수신거부리스트 조회후  처리
	 *                 업무코드는 EXTEND 내용중 '|' 기준 2번째 문자열의
	 *                 3자리 사용 
	 * BC요구사항 20140711 : 743업무코드를 수신거부를 체크하지 않도록 요청하여 주석처리
	 *                       추후에 변경될수 있음
	 * BC요구사항 20140729 : 743업무코드를 수신거부를 체크
	 * BC요구사항 20140804 : 캠페인ID "1000362328" 수신거부 체크 - 한시 적용, 추후요청시 제거
	 * BC요구사항 20140901 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000374597" 
	 * BC요구사항 20140902 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000362589" "1000375197"
	 * BC요구사항 20140926 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000402908" "1000382336" "1000382337" "1000408472" 
	 * BC요구사항 20141010 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000410372" "1000410370" 
	 * BC요구사항 20141016 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000412251"
	 * BC요구사항 20141031 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000412690" "1000412691" "1000412692"
	 * BC요구사항 20141107 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000419319" "1000419418" "1000419420"
	 * BC요구사항 20141114 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000422951"
	 * BC요구사항 20141128 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000425036" "1000425032" "1000425035" "1000425037" "1000431473"
	 * BC요구사항 20141204 : 743일경우에도 해당 캠페인ID는 거부자 포함해서 전송 - 캠페인ID "1000433578"
	 * BC요구사항 20141016 : 위의 특정 캠페인ID는 무조건 블랙리스트만 체크
	 */
	 
//	char	*job_code;
//	char	*camp_id;
//	char	*comp_id;
//	char	*cust_no;
//	char	*appr_dt;
//	char	*appr_tp;
//    char	*szResvData;
//    char	szExtend[128];
//	memset(szExtend, 0x00, sizeof(szExtend));


	char	szExtend[128];
	char	job_code[ 24];
	char	camp_id [ 24];
	char	comp_id [ 24];
	char	cust_no [ 24];
	char	appr_dt [ 24];
	char	appr_tp [ 24];
	char	szResvData[128];
	int	ii = 0;
	int	jj = 0;
	int	zz = 0;
	
	memset(szExtend  , 0x00, sizeof(szExtend  ));
	memset(job_code  , 0x00, sizeof(job_code  ));
	memset(camp_id   , 0x00, sizeof(camp_id   ));
	memset(comp_id   , 0x00, sizeof(comp_id   ));
	memset(cust_no   , 0x00, sizeof(cust_no   ));
	memset(appr_dt   , 0x00, sizeof(appr_dt   ));
	memset(appr_tp   , 0x00, sizeof(appr_tp   ));	
	memset(szResvData, 0x00, sizeof(szResvData));	
	
    if( pBcastData == NULL )
    {
    	strcpy(szExtend, mmsPacketSend.getExtendValue());
    	strcpy(szResvData ,strchr(szExtend, '|'));
    	//szResvData = strchr(szExtend, '|');
		log_history(0,0,"szResvData[%s]mmsPacketSend.getExtendValue[%s]",szResvData+1, mmsPacketSend.getExtendValue());
    	//strcpy(senderDbMMSTBL.szResvData,mmsPacketSend.getExtendValue());
	}
    else
    {
    	strcpy(szExtend, pBcastData->strExtend.c_str());
    	strcpy(szResvData ,strchr(szExtend, '|'));
    	//szResvData = strchr(szExtend, '|');
		log_history(0,0,"szResvData[%s]pBcastData->strExtend.c_str()[%s]",szResvData+1, pBcastData->strExtend.c_str());
    	//strcpy(senderDbMMSTBL.szResvData,pBcastData->strExtend.c_str());
	}
    strcpy(senderDbMMSTBL.szResvData,szResvData+1);
//	job_code = strtok(szExtend, "|");
//	camp_id = strtok(NULL, "|");	
//	comp_id = strtok(NULL, "|");   		
//	cust_no = strtok(NULL, "|");
//	appr_dt = strtok(NULL, "|");
//	appr_tp = strtok(NULL, "|");
	
	for (ii = 0; ii < strlen(szExtend); ii++)
	{
		if (szExtend[ii] == '|')
		{
			jj++;
			zz = 0;
			continue;
		}
		else
		{
			switch(jj)
			{
				case	0:
					job_code[zz] = szExtend[ii];break;
				case	1:
					camp_id[zz]  = szExtend[ii];break;
				case	2:       
					comp_id[zz]  = szExtend[ii];break;
				case	3:       
					cust_no[zz]  = szExtend[ii];break;
				case	4:       
					appr_dt[zz]  = szExtend[ii];break;
				case	5:       
					appr_tp[zz]  = szExtend[ii];break;
			}
			zz++;
		}
	}	
	
	if (memcmp(job_code, "743", 3) == 0 || memcmp(camp_id, "1000362328", 10) == 0)
	{ 
		ret = 1;	
	}
	
	/*
	 * 아래 해당 캠페인ID는 무조건 블랙리스트 체크
	 * 화이트리스트 체크 안함
	 */
	if (memcmp(camp_id, "1000374597", 10) == 0 || 
		memcmp(camp_id, "1000362589", 10) == 0 || 
		memcmp(camp_id, "1000375197", 10) == 0 || 
		memcmp(camp_id, "1000402908", 10) == 0 || 
		memcmp(camp_id, "1000382336", 10) == 0 || 
		memcmp(camp_id, "1000382337", 10) == 0 || 
		memcmp(camp_id, "1000408472", 10) == 0 || 
		memcmp(camp_id, "1000410372", 10) == 0 || 
		memcmp(camp_id, "1000410370", 10) == 0 || 
		memcmp(camp_id, "1000412251", 10) == 0 || 
		memcmp(camp_id, "1000412690", 10) == 0 || 
		memcmp(camp_id, "1000412691", 10) == 0 || 
		memcmp(camp_id, "1000412692", 10) == 0 || 
		memcmp(camp_id, "1000419319", 10) == 0 || 
		memcmp(camp_id, "1000419418", 10) == 0 || 
		memcmp(camp_id, "1000419420", 10) == 0 || 
		memcmp(camp_id, "1000422951", 10) == 0 || 
		memcmp(camp_id, "1000425036", 10) == 0 || 
		memcmp(camp_id, "1000425032", 10) == 0 || 
		memcmp(camp_id, "1000425035", 10) == 0 || 
		memcmp(camp_id, "1000425037", 10) == 0 || 
		memcmp(camp_id, "1000431473", 10) == 0 || 
		memcmp(camp_id, "1000433578", 10) == 0)
	{
		ret = 1;	
	}
	
	/*
	 * 승일일자와 다르면 전송금지
	 */		
	struct timespec tmv;
	struct tm	tp;
	char	szDate[8+1];
	
	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);	
	sprintf(szDate, "%04d%02d%02d", tp.tm_year+1900, tp.tm_mon+1, tp.tm_mday);
log_history(0,0,"szDate[%s]appr_dt[%s]", szDate, appr_dt);
	if (appr_dt != NULL)
	{
		if (strcmp(szDate, appr_dt) != 0)
		{
			ret = 1;	
		}
	}
	
	/*
	 * WiN-K 시스템 활용하는 LMS
	 * 특정전화번호는 무조건 영구제외 추가
	 */		
	if (memcmp(senderDbMMSTBL.szDstAddr, "01047167091", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01041365582", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01071478518", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01037333493", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01029969430", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01088929558", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01045500919", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01032693100", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01093471039", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "01092091535", 11) ==0 ||
		memcmp(senderDbMMSTBL.szDstAddr, "0186816811", 10) ==0 )
	{
		ret = 1;
	}
	
	log_history(0,0,"job_code[%s]camp_id[%s]ret[%d]", job_code, camp_id, ret);


    strcpy(senderDbMMSTBL.szCid,szSenderID);
    senderDbMMSTBL.nMsgType = 0;
    senderDbMMSTBL.nPriority = priority;
    senderDbMMSTBL.nCtnId = ctnid;

    senderDbMMSTBL.nCtnType = mmsPacketSend.getCtnType();

    senderDbMMSTBL.nRgnRate = 65;
    senderDbMMSTBL.nInterval = 10;
    senderDbMMSTBL.nTextCnt = mmsPacketSend.getTextCnt();
    senderDbMMSTBL.nImgCnt = mmsPacketSend.getImgCnt();
    senderDbMMSTBL.nAugCnt = mmsPacketSend.getAugCnt();
    senderDbMMSTBL.nMpCnt = mmsPacketSend.getMpCnt();
    senderDbMMSTBL.nMMSId = nMMSId;


    CSenderDbInfoAck senderDbInfoAck;

    CDBUtil dbUtil;

    dbUtil.sendQuery(
            db,
            (void*)&senderDbMMSTBL,
            (void*)&senderDbInfoAck,
            gConf.dbRequestTimeOut,
            gConf.senderDBName);

    return ret;
}
int setMMSMSG2DB(CKSSocket& db, char* szCid, int nMMSId,int ctnid, CMMSFileProcess& mmsFileProcess,int priority, int type, CMMSPacketSend& mmsPacketSend, CBcastData * pBcastData)
{
    int ret;
    time_t ThisT,LastT;
    CSenderDbMMSMSG senderDbMMSMSG;
    time(&LastT);

    memset(&senderDbMMSMSG,0x00,sizeof(CSenderDbMMSMSG));
    
    /*
     * 파트너스 메시지 22~06시 전송금지 (20140922)
     * 22~06시간 전송금지로 인해 필터대상으로 처리
     * SenderDB 프로세스에서 필터적용
     */
	struct tm	tp;
	time_t  curr_tm;
	
	time(&curr_tm);
	localtime_r(&curr_tm, &tp);
	if (tp.tm_hour >= 22 || tp.tm_hour < 6)
	{
		type = 1;
		log_history(0,0,"[REJ] PARTNERS NIGHT SEND reject MMSId[%d]", nMMSId);
	}	
    /*
     * type - 1: 필터대상, 0: 필터미대상
     */
    if (type)
    	senderDbMMSMSG.header.type = SETSENDQUE_FILTER;
    else
    	senderDbMMSMSG.header.type = SETSENDQUE;
    senderDbMMSMSG.header.leng = sizeof(CSenderDbMMSMSG) - sizeof(Header);

	//큐 NUMBER 저장
    sprintf(senderDbMMSMSG.szQName,"%d", getTelcoId(mmsPacketSend.getImgCnt(), gConf.smsTelcoInfo, mmsFileProcess.getTxtColorYN()));
	printf("queue : [%s]\n", senderDbMMSMSG.szQName);

    senderDbMMSMSG.nPriority = priority;
    senderDbMMSMSG.nCtnId = ctnid;

    if( pBcastData == NULL)
		strcpy(senderDbMMSMSG.szDstAddr,mmsPacketSend.getReceiverValue());
    else
		strcpy(senderDbMMSMSG.szDstAddr,pBcastData->strReceiver.c_str());
		
    strcpy(senderDbMMSMSG.szCallBack,mmsPacketSend.getSenderValue());
    strcpy(senderDbMMSMSG.szMsgTitle,mmsPacketSend.getSubjectValue());
    senderDbMMSMSG.nCntType = mmsPacketSend.getCtnType();

    if( strcmp(senderDbMMSMSG.szQName,"1") == 0 ) {
        sprintf(senderDbMMSMSG.szTxtPath,"TXT_SKT/%s",mmsFileProcess.getTxtPath());
    } else  if  ( strcmp(senderDbMMSMSG.szQName,"2") == 0 ) {
        sprintf(senderDbMMSMSG.szTxtPath,"TXT_KTF/%s",mmsFileProcess.getTxtPath());
    } else  if  ( strcmp(senderDbMMSMSG.szQName,"3") == 0 ) {
        //sprintf(senderDbMMSMSG.szTxtPath,"TXT_LGT/%s",mmsFileProcess.getTxtPath());
		sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
    } else  if  ( strcmp(senderDbMMSMSG.szQName,"4") == 0 ) {
        sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
    } else  if  ( strcmp(senderDbMMSMSG.szQName,"21") == 0 ) {
        sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
    } else  if  ( strcmp(senderDbMMSMSG.szQName,"25") == 0 ) {
        sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
    } else {
        sprintf(senderDbMMSMSG.szTxtPath,"TXT_SSN/%s",mmsFileProcess.getTxtPath());
    }

	// 이미지만 전송할 경우 201309
	if(mmsPacketSend.getTextCnt() == 0 && mmsPacketSend.getImgCnt() >= 1)
	{
		memset(senderDbMMSMSG.szTxtPath, 0x00, sizeof(senderDbMMSMSG.szTxtPath));
	}
		
    senderDbMMSMSG.nRgnRate = 65;
    senderDbMMSMSG.nInterval = 10;
    senderDbMMSMSG.nMMSId = nMMSId;



    CSenderDbInfoAck senderDbInfoAck;
	/*
	 * 수신동의자 Filter 추가로 인하여 고객번호 SenderDB에 전송
	 * 넘겨줄 Buff가 없어 senderDbMMSMSG.nInterval 담아서 전송  
	 * EXTEND(부가정보) 
	 * 업무코드|캠패인|가맹점|고객
	 * 캠패인|가맹점|고객 - TBL_MMS_SEND의 RESV_DATA컬럼에 등록됨
	 * ex) 743|100001264|743517933|69789001
	 */
//	char	*job_code;
//	char	*camp_id;
//	char	*comp_id;
//	char	*cust_no;
//	char	*appr_dt;
//	char	*appr_tp;
//    char	*szResvData;
//    char	szExtend[128];
//	memset(szExtend, 0x00, sizeof(szExtend));
//	strcpy(szExtend, mmsPacketSend.getExtendValue());
//	szResvData = strchr(szExtend, '|');
//    strcpy(senderDbMMSMSG.szResvData,szExtend);
//	
//	job_code = strtok(szExtend, "|");    		
//	camp_id = strtok(NULL, "|");   		
//	comp_id = strtok(NULL, "|");   		
//	cust_no = strtok(NULL, "|");
//	appr_dt = strtok(NULL, "|");
//	appr_tp = strtok(NULL, "|");
//    senderDbMMSMSG.nInterval = atoi(cust_no);
//	log_history(0,0,"FILTER_TYPE[%d]: job_code[%s]camp_id[%s]appr_dt[%s]appr_tp[%s] MMSId[%d]", 
//								type, job_code, camp_id, appr_dt, appr_tp, nMMSId);	


	char	szExtend[128];
	char	job_code[ 24];
	char	camp_id [ 24];
	char	comp_id [ 24];
	char	cust_no [ 24];
	char	appr_dt [ 24];
	char	appr_tp [ 24];
	char	szResvData[128];
	int	ii = 0;
	int	jj = 0;
	int	zz = 0;
	
	memset(szExtend  , 0x00, sizeof(szExtend  ));
	memset(job_code  , 0x00, sizeof(job_code  ));
	memset(camp_id   , 0x00, sizeof(camp_id   ));
	memset(comp_id   , 0x00, sizeof(comp_id   ));
	memset(cust_no   , 0x00, sizeof(cust_no   ));
	memset(appr_dt   , 0x00, sizeof(appr_dt   ));
	memset(appr_tp   , 0x00, sizeof(appr_tp   ));
	memset(szResvData, 0x00, sizeof(szResvData));
	
	strcpy(szExtend, mmsPacketSend.getExtendValue());
    strcpy(szResvData ,strchr(szExtend, '|'));
	//szResvData = strchr(szExtend, '|');
	strcpy(senderDbMMSMSG.szResvData,szExtend);
	
	for (ii = 0; ii < strlen(szExtend); ii++)
	{
		if (szExtend[ii] == '|')
		{
			jj++;
			zz = 0;
			continue;
		}
		else
		{
			switch(jj)
			{
				case	0:
					job_code[zz] = szExtend[ii];break;
				case	1:
					camp_id[zz]  = szExtend[ii];break;
				case	2:       
					comp_id[zz]  = szExtend[ii];break;
				case	3:       
					cust_no[zz]  = szExtend[ii];break;
				case	4:       
					appr_dt[zz]  = szExtend[ii];break;
				case	5:       
					appr_tp[zz]  = szExtend[ii];break;
			}
			zz++;
		}
	}	
	
	senderDbMMSMSG.nInterval = atoi(cust_no);
	log_history(0,0,"FILTER_TYPE[%d]: job_code[%s]camp_id[%s]appr_dt[%s]appr_tp[%s] MMSId[%d] cust_no[%d]", 
								type, job_code, camp_id, appr_dt, appr_tp, nMMSId, senderDbMMSMSG.nInterval);
	
    CDBUtil dbUtil;

    dbUtil.sendQuery(
            db,
            (void*)&senderDbMMSMSG,
            (void*)&senderDbInfoAck,
            gConf.dbRequestTimeOut,
            gConf.senderDBName);

	log_history(0,0,"sendQuery END[%s][%s]",senderDbInfoAck.szResult, dbUtil.getErrorMsg());	
    return 0;
}


int getTelcoId(int imgCnt, char* szTelco, int nColorYN)
{
    char* p;
    int telcoArray[3];
    int i=0;
    memset(telcoArray,0x00,sizeof(telcoArray));

    p = strtok(szTelco,"|");
    if( p == NULL ) return 1;
    telcoArray[i++] = atoi(p);

    while(p = strtok(NULL,"|") )
    {
        telcoArray[i++]= atoi(p);
        if( i >= 3 ) break;
    }

	//szTelco 다시 복구.버그 수정.2012.12.10.
	sprintf(szTelco, "%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2]);

	//컬러 문자를 우선으로 통신사를 체크한다.
	//컬러 LMS/MMS
	if (nColorYN == 1)
	{
		//printf("telcoArray[2]:%d\n", telcoArray[2]);
		return telcoArray[2];	//컬러
	}
	//일반 LMS
	else if (imgCnt == 0)
	{
		//printf("telcoArray[0]:%d\n", telcoArray[0]);
		return telcoArray[0];	//LMS
	}
	//일반 MMS
	else if (imgCnt > 0)
	{
		//printf("telcoArray[1]:%d\n", telcoArray[1]);
		return telcoArray[1];	//MMS
	}

    return 1;
}

char *matchString(char* szOrg, char* szTag, string& strVal)
{

    char* szStt;          
    char* szEnd=NULL;          
    strVal = "";
    strVal.reserve(0);

    if( szOrg == NULL )
        return NULL;
    if ((szStt=strstr(szOrg,szTag))) {
        szStt=szStt+strlen(szTag)+1;   
        szEnd=strstr(szStt,"\r\n");    
        if( (szEnd - szStt) == 0 )
        {
            strVal = "";
            strVal.reserve(0);
//            strVal = "\r\n";

        } else {
        //    return NULL;
            strVal = "";
            strVal.reserve(0);
            if( szEnd == NULL )
                strVal.insert(0,szStt);
            else
                strVal.insert(0,szStt,szEnd-szStt);
        }
//        memcpy(szVal,szStt,szEnd-szStt);       
    }
    if( szStt == NULL ) return NULL;
    if( szEnd == NULL ) return NULL;
    return szEnd+2;

}

int startProcess(char* szConfigFile)
{
    int pid;
    char tmpPath[512];
    char processName[512];
	char logMsg[512];

	char tmpname[512];

	strcpy(tmpname,"bshopsender");
	strcpy(processName,"/user/www/work/bshopsend.php");

	log_history(0,0,"startProcess[%s]",szConfigFile);

    if( (pid = fork()) == 0 )
    {

        memset(tmpPath,0x00,sizeof(tmpPath));
        sprintf(tmpPath,"/user/www/work/");
        if (chdir(tmpPath) < 0)
        {
            log_history(0,0,"[startProcess] %s chdir error.[%s]",tmpPath,strerror(errno));
            exit(0);
        }
        if (execlp(processName,tmpname,szConfigFile , (char*)0)  < 0 )
        {
            log_history(0,0,"[startProcess] %s execlp error.<%s>",
                    processName, strerror(errno));
            exit(0);
        }
    } else if( pid < 0 ) {
        log_history(0,0,"startProcess Error [%s]",strerror(errno));
        return -1;
    } 

    return 0;
}


/** @return result
 *
 * 94 : Timeout (logon)
 */
int requestLogon(int sockfd,CLogonDbInfo & logonDbInfo)
{
    int ret;
    CKSSocket conn;
    CKSSocket customConn;
    char buff[SOCKET_BUFF];
    int nRecvLen;
    int nResult;


    customConn.attach(sockfd);

    ret = customConn.select();
    if( ret == 0 )
    {
        log_history(0,0,"Peer Timeout Error(%s)",strerror(errno));
        return -1;
    }

    if( ret < 0 )
    {
        log_history(0,0,"Peer Seelect Error(%s)",strerror(errno));
        return -1;
    }

    memset(buff,0x00,SOCKET_BUFF);
    nRecvLen = customConn.recv(buff,SOCKET_BUFF);
    if( nRecvLen == 0 ) {
        log_history(0,0,"Close by Peer(%s)",strerror(errno));
        return -1;
    }
    if( nRecvLen < 0 ) {
        log_history(0,0,"Peer Socket Recv Error(%s)",strerror(errno));
        return -1;
    }
   // viewPack [info] 
    //viewPack(buff,nRecvLen);
    ret = conn.connectDomain(gConf.logonDBName);
    if( ret != 0 )
    {
        log_history(0,0,"LogonDB conn Error",strerror(ret));
        return -1;
    }

    ret = conn.send(buff,nRecvLen);
    if( ret != nRecvLen )
    {
        log_history(0,0,"LogonDB send Error",strerror(errno));
        conn.close();
        return -1;
    }

    memset(buff,0x00,SOCKET_BUFF);
    nRecvLen = conn.recv(buff,SOCKET_BUFF);
    if( nRecvLen == 0 ) {
        log_history(0,0,"Close by Logon Domain(%s)",strerror(errno));
        conn.close();
        return -1;
    }
    if( nRecvLen < 0 ) {
        log_history(0,0,"Logon Domain Socket Recv Error(%s)",strerror(errno));
        conn.close();
        return -1;
    }
/*
    CLogonDbInfo* pRcvData = (CLogonDbInfo*)buff;
    TypeMsgBindAck Ack;
    memset(&Ack,0x00,sizeof(Ack));
    strcpy(Ack.header.msgType,"2");
    strcpy(Ack.header.msgLeng,"2");
    if( pRcvData->nmRST == 0 )
        memcpy(Ack.szResult,"00",2);
    else 
        memcpy(Ack.szResult,"33", 2);
*/
    ret = customConn.send(buff,nRecvLen);
    if( ret != nRecvLen )
    {
        log_history(0,0,"Peer send Error",strerror(errno));
        conn.close();
        return -1;
    }

    ret = conn.send("OK",3);
    if( ret != 3 )
    {
        log_history(0,0,"Logon Domain Error (%s)",strerror(errno));
        conn.close();
        return -1;
    }

    memset(buff,0x00,SOCKET_BUFF);
    nRecvLen = conn.recv(buff,SOCKET_BUFF);
    if( nRecvLen == 0 ) {
        log_history(0,0,"Close by Logon Domain(%s):Error",strerror(errno));
        conn.close();
        return -1;
    }
    if( nRecvLen < 0 ) {
        log_history(0,0,"Logon Domain Socket Recv Error(%s)",strerror(errno));
        conn.close();
        return -1;
    }

    conn.close();

    CLogonDbInfo* pRcvData = (CLogonDbInfo*)buff;

    if( pRcvData->nmRST == 0 )
    {
        memcpy(&logonDbInfo,pRcvData,sizeof(logonDbInfo));
        return 0;
    }
    else 
        return -1;
}


/* @brief logonInfo에서 가져온 IP:PORT 를 정보와 일치하는지 체크
 * @return succ 0 fail -1
 */
int checkServerInfo(CLogonDbInfo& logonDbInfo,char* szIP,int nPort)
{
    char szCmpString[32];

    CCL(szCmpString);
    sprintf(szCmpString,"%s:%d",szIP,nPort);

    /* 값이 없으면 체크 하지 않는다 */
    if( strlen(logonDbInfo.szServerInfo) != 0 )
    {
        if( strstr(logonDbInfo.szServerInfo,szCmpString) == 0 )
        {
            log_history(0,0,"ServerInfo[%s] This[%s] Error",
                    logonDbInfo.szServerInfo,
                    szCmpString);
            return -1;
        }
    }


    /* 허용 아이피 체크 0.0.0.0 : 모두 허용 */
    if( strstr(logonDbInfo.szSIP,"0.0.0.0") != NULL )
    {
        if( strstr(logonDbInfo.szSIP,logonDbInfo.szIP) != NULL )
        {
            log_history(0,0,"ServerInfoSIP[%s] customIP[%s] Error",
                    logonDbInfo.szSIP,
                    logonDbInfo.szIP);
            return -2;
        }
    }







    return 0;
}



int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.serverIP , conf.FetchEntry("gw.ip"),16);
    if( gConf.serverIP == NULL ) strcpy(gConf.serverIP,"");

    conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
    if( gConf.logonDBName == NULL ) strcpy(gConf.logonDBName,"");

    gConf.serverPORT = conf.FetchEntryInt("gw.port");
    gConf.process_sleep_time = conf.FetchEntryInt("process.sleeptime");


    conf.strncpy2(gConf.bindir , conf.FetchEntry("process.bindir"),64);
    if( gConf.bindir == NULL ) strcpy(gConf.bindir,"");

    conf.strncpy2(gConf.cfgdir , conf.FetchEntry("process.cfgdir"),64);
    if( gConf.cfgdir == NULL ) strcpy(gConf.cfgdir,"");

    conf.strncpy2(gConf.logPath , conf.FetchEntry("log.path"),64);
    if( gConf.logPath == NULL ) strcpy(gConf.logPath,"");

    conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.path"),64);
    if( gConf.domainPath == NULL ) strcpy(gConf.domainPath,"");

    gConf.dbRequestTimeOut = conf.FetchEntryInt("db.requesttimeout");
    if( gConf.dbRequestTimeOut <= 0 )
        gConf.dbRequestTimeOut = 1;
        
    conf.strncpy2(gConf.ContentPath , conf.FetchEntry("path.mmscontent"),64);
    if( gConf.ContentPath == NULL ) strcpy(gConf.ContentPath,"");
    	
    conf.strncpy2(gConf.senderDBName , conf.FetchEntry("domain.senderdb"),64);
    if( gConf.senderDBName == NULL ) strcpy(gConf.senderDBName,"");
    	
    conf.strncpy2(gConf.smsTelcoInfo , conf.FetchEntry("db.smsTelcoInfo"),64);
    if( gConf.smsTelcoInfo == NULL ) strcpy(gConf.smsTelcoInfo,"");

    return 0;
}

int checkLoginDup(CLogonDbInfo &logonDbInfo)
{


    return 0;
}


void writeLogMMSData(CMMSPacketSend& mmsPacketSend,int mmsid, int ctnid)
{
	log_history(0,0,"[SND] mmsid[%d] ctnid[%d] key[%s] extend[%s] subject[%s] dst[] call[%s] contentCnt[%s] s[%s] k[%s] l[%s] txt[%s] img[%s] mp[%s] bcast[%s]",
            mmsid,
            ctnid,
            mmsPacketSend.getKeyValue(),
            mmsPacketSend.getExtendValue(),
            mmsPacketSend.getSubjectValue(),
         /*   mmsPacketSend.getReceiverValue(),*/
            mmsPacketSend.getSenderValue(),
            mmsPacketSend.getContentCntValue(),
            mmsPacketSend.getPsktynValue(),
            mmsPacketSend.getPktfynValue(),
            mmsPacketSend.getPlgtynValue(),
            mmsPacketSend.getTextCntValue(),
            mmsPacketSend.getImgCntValue(),
            mmsPacketSend.getAudCntValue(),
            mmsPacketSend.getMpCntValue(),
            mmsPacketSend.getBcastCntValue()
            );

}

