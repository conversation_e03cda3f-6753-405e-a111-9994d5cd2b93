#include "reportDB.h"
#include <queue>
#include <list>
#include <semaphore.h> 

EXEC SQL BEGIN DECLARE SECTION;
#define MAX_DB_CONNECTION 2
EXEC SQL END DECLARE SECTION;

void Init_Oracle(sql_context ctx);

void* doService(void* param);
int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff,CReportDbInfo& reportDbInfo);

queue<void*, list<void*> > dbConnQ;
sem_t m_sem;

int main(int argc, char* argv[])
{
    int ret;
    char logMsg[1024];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;
    CKSThread ksthread;
    CThreadInfo* pThreadInfo;
    char buff[1024];
    int hNewSocket;
    TypeMsgBindSnd* pLogonData;
    int i;

    EXEC SQL BEGIN DECLARE SECTION;
    sql_context db[MAX_DB_CONNECTION];
    EXEC SQL END DECLARE SECTION;



    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }

    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server();


    EXEC SQL ENABLE THREADS;
    for(i=0;i<MAX_DB_CONNECTION;i++)
    {
        EXEC SQL CONTEXT ALLOCATE :db[i];

        Init_Oracle(db[i]);
        if( sqlca.sqlcode !=0 )
        {
            monitoring("db connection fail error",0,errno);
            ml_sub_end();
            return -1;
        }

        log_history(0,0,"db[%x]",db[i]);
        dbConnQ.push(db[i]);
    }




    sem_init(&m_sem,1,1);

    ret = svrSockfd.createDomainNon(DOMAIN_REPORTDB);
    if( ret !=0 ) {
        log_history(0,0,"DOMAIN_REPORTDB 생성실패",strerror(errno));
        goto END;
    }

    

    while(activeProcess)
    {
        wait_a_moment(10000);
        hNewSocket = svrSockfd.accept();
        if( hNewSocket <= 0 )
            continue;


        /* new connection  */
        pThreadInfo = NULL;
        pThreadInfo = new CThreadInfo;
        if( pThreadInfo == NULL )
        {
            log_history(0,0,"new ThreadInfo Error [%s]",
                    strerror(errno));
            close(hNewSocket);
            continue;
        }

/*    log_history(0,0,"org[%x]",pThreadInfo); */
        pThreadInfo->sock = hNewSocket;
        ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
        if( ret != 0 )
        {
            log_history(0,0,"create thread Error [%s]",
                    strerror(errno));
            close(pThreadInfo->sock);
            delete pThreadInfo;
            continue;
        }
/*        delete pThreadInfo; */

    }

END:
    svrSockfd.close();


    sem_destroy(&m_sem);

    for(i=0;i<MAX_DB_CONNECTION;i++)
    {
        EXEC SQL CONTEXT USE :db[i];
        EXEC SQL COMMIT WORK RELEASE;
        EXEC SQL CONTEXT FREE :db[i];
    }


    
    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}


void* doService(void* param)
{
     pthread_detach(pthread_self()); 
    int ret;
    int chozo99=0;
    CKSSocket newSockfd;
    char buff[SOCKET_BUFF];
    CThreadInfo* info = (CThreadInfo*)param;
    CReportDbInfo reportDbInfo;

    void* pDB;
    struct sqlca sqlca;

    newSockfd.attach(info->sock);
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 ) {
        newSockfd.close();
        log_history(0,0,"connection 후 3초간 데이터가 없어 연결을 close 합니다");
        delete info;
        return NULL;
    }

    if( ret < 0 ) {
        newSockfd.close();
        log_history(0,0,"recv Error");
        delete info;
        return NULL;
    }


REGETDB:
    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        { 
            newSockfd.close();
            log_history(0,0,"세마포어 wait Error");
            delete info;
            return NULL;
        }

    pDB = dbConnQ.front();
    if( pDB != NULL ) dbConnQ.pop();

    if( sem_post(&m_sem) == -1 )
    {
        newSockfd.close();
        log_history(0,0,"세마포어 해제 Error [%s]",strerror(errno));
        delete info;
        return NULL;
    }

    if( pDB == NULL )
    {
        ret = 0;
        goto NODATA;
    }

    memset(&reportDbInfo,0x00,sizeof(reportDbInfo));
    ret = getReportDB(pDB,sqlca,buff,reportDbInfo);


    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        { 
            newSockfd.close();
            log_history(0,0,"세마포어 wait Error");
            delete info;
            return NULL;
        }

    dbConnQ.push(pDB);

    if( sem_post(&m_sem) == -1 )
    {
        newSockfd.close();
        log_history(0,0,"세마포어 해제 Error [%s]",strerror(errno));
        delete info;
        return NULL;
    }


    if( ret < 0 )
    {
        newSockfd.close();
        delete info;
        return NULL;
    }

NODATA:
    if( ret == 0 )
        memcpy(reportDbInfo.szResCode,"99",2);
    newSockfd.send((char*)&reportDbInfo,sizeof(reportDbInfo));

    newSockfd.close();

    delete info;

    return NULL;
}



int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff ,CReportDbInfo& reportDbInfo)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char szPtnsn[16+1];
    char szResCode[16+1];
    int nTelcoId =-1;
    char szResvData[2048];
/*    char szDstAdr[16];
 */
    char szEndTelco[8];
    int nMsgId=-1;
    char szRptDate[16];
    int nJobCode=-1;
/*    int nCnt=-1;
 */
    int nSqlCode=-1;
/*    int szSqlErrorMsg[1024];
 */
/*    char szAppName[32];
 */
    EXEC SQL END DECLARE SECTION;

    EXEC SQL BEGIN DECLARE SECTION;


    char szTelco[36];
    int nStatus = -1;
    int nCnt = -1;
    char szSqlErrorMsg[1024];
    int nmPID;
    char szPID[8];
    char szJOB[8];
    int nPID;
    int nJOB;


    char szAppName[16];
    char szPRT[4];
    int otResult = -1;
    char szSerial[16+1];
    char szDstadr[16+1];
    char szCalbck[16+1];
    char szSndmsg[100+1];
    int nPRT;


    EXEC SQL END DECLARE SECTION;


    CCL(szTelco);
    CCL(szSqlErrorMsg);
    CCL(szPID);
    CCL(szJOB);
/*
    nPID = data->logonDbInfo.nmPID;
    nJOB = data->logonDbInfo.nmJOB;
    */


    CCL(szPtnsn);
    CCL(szResCode);
    CCL(szResvData);
    CCL(szDstadr);
    CCL(szEndTelco);
    CCL(szRptDate);
    CCL(szSqlErrorMsg);
    CCL(szAppName);

    memcpy(szAppName,buff,strlen(szAppName));
    strcpy(szAppName,"2_1");

    EXEC SQL CONTEXT USE :pDB;
    EXEC SQL EXECUTE
        BEGIN
        proc_get_report_time(
                in_appname=>:szAppName,
                ptn_sn=>:szPtnsn,
                res_code=>:szResCode,
                telco_id=>:nTelcoId,
                resv_data=>:szResvData,
                dstaddr=>:szDstadr,
                end_telco=>:szEndTelco,
                msg_id=>:nMsgId,
                telco_rpt_date=>:szRptDate,
                ot_job_code=>:nJobCode,
                ot_cnt=>:nCnt,
                ot_sqlcode=>:nSqlCode,
                ot_sqlmsg=>:szSqlErrorMsg); 
    END;
    END-EXEC;
    if( memcmp(szResCode,"99",2) == 0 || memcmp(szResCode,"98",2) == 0 )
        return 0;

    memcpy(reportDbInfo.szPtnsn,szPtnsn,sizeof(szPtnsn));
    memcpy(reportDbInfo.szResCode,szResCode,sizeof(szResCode));
    reportDbInfo.nTelcoId = nTelcoId;
    memcpy(reportDbInfo.szResvData,szResvData,sizeof(szResvData));
    memcpy(reportDbInfo.szDstAdr,szDstadr,sizeof(szDstadr));
    memcpy(reportDbInfo.szEndTelco,szEndTelco,sizeof(szEndTelco));
    reportDbInfo.nMsgId = nMsgId;
    memcpy(reportDbInfo.szRptDate,szRptDate,sizeof(szRptDate));
    reportDbInfo.nJobCode = nJobCode;
    reportDbInfo.nCnt = nCnt;
    reportDbInfo.nSqlCode = nSqlCode;

    return 1;
}



void Init_Oracle(sql_context ctx)
{
    EXEC SQL BEGIN DECLARE SECTION;
    VARCHAR Username[10];
    VARCHAR Password[10];
    VARCHAR dbstring[10];
    EXEC SQL END DECLARE SECTION;

    strcpy((char*)Username.arr, "neosms");
    Username.len = strlen((char*)Username.arr);
    strcpy((char*)Password.arr, "neosms");
    Password.len = strlen((char*)Password.arr);
    strcpy((char*)dbstring.arr, "u60");
    dbstring.len = strlen((char*)dbstring.arr);

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
}


